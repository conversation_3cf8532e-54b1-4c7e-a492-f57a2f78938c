#!/usr/bin/env node

/**
 * Find Working Test Accounts
 * 
 * Try different test account combinations to find what actually works
 */

const { chromium } = require('playwright');

// Potential test accounts to try
const POTENTIAL_ACCOUNTS = [
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  { email: '<EMAIL>', password: 'TestPassword123!' },
  // Add more potential combinations
  { email: '<EMAIL>', password: 'password123' },
  { email: '<EMAIL>', password: 'Password123!' },
  { email: '<EMAIL>', password: 'password123' },
];

async function findWorkingAccounts() {
  console.log('🔍 Finding Working Test Accounts');
  console.log('='.repeat(40));

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  const workingAccounts = [];
  const failedAccounts = [];

  try {
    for (let i = 0; i < POTENTIAL_ACCOUNTS.length; i++) {
      const account = POTENTIAL_ACCOUNTS[i];
      console.log(`\n🧪 Testing account ${i + 1}/${POTENTIAL_ACCOUNTS.length}: ${account.email}`);
      
      try {
        // Navigate to login page
        await page.goto('http://localhost:5173');
        await page.waitForLoadState('networkidle');
        
        // Check if login form is present
        const emailInput = page.locator('input[type="email"]').first();
        const passwordInput = page.locator('input[type="password"]').first();
        const submitButton = page.locator('button[type="submit"]').first();
        
        const hasLoginForm = await emailInput.isVisible({ timeout: 3000 });
        
        if (!hasLoginForm) {
          console.log('  ⚠️  No login form found, skipping...');
          continue;
        }
        
        // Clear and fill form
        await emailInput.clear();
        await emailInput.fill(account.email);
        await passwordInput.clear();
        await passwordInput.fill(account.password);
        
        // Submit
        await submitButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Check if login was successful
        const stillOnLogin = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
        
        if (!stillOnLogin) {
          console.log('  ✅ SUCCESS! Account works');
          workingAccounts.push(account);
          
          // Take screenshot of successful login
          await page.screenshot({ 
            path: `test-results/working-account-${account.email.replace('@', '-at-').replace('.', '-')}.png`, 
            fullPage: true 
          });
          
          // Check what page we're on
          const currentUrl = page.url();
          const bodyText = await page.locator('body').textContent();
          
          console.log(`    URL: ${currentUrl}`);
          console.log(`    Page content preview: ${bodyText?.substring(0, 100)}...`);
          
          // Look for bento grid
          const bentoElements = await page.locator('[data-canvas-card]').count();
          const gridElements = await page.locator('.grid').count();
          
          console.log(`    Bento cards found: ${bentoElements}`);
          console.log(`    Grid elements found: ${gridElements}`);
          
        } else {
          console.log('  ❌ Failed - still on login page');
          
          // Check for error messages
          const errorMessages = await page.locator('[class*="error"], [class*="alert"], .text-red-500, .text-danger').allTextContents();
          if (errorMessages.length > 0) {
            console.log(`    Error messages: ${errorMessages.join(', ')}`);
          }
          
          failedAccounts.push({ ...account, errors: errorMessages });
        }
        
      } catch (error) {
        console.log(`  ❌ Error testing account: ${error.message}`);
        failedAccounts.push({ ...account, error: error.message });
      }
      
      // Small delay between attempts
      await page.waitForTimeout(1000);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }

  // Summary
  console.log('\n📋 RESULTS SUMMARY');
  console.log('='.repeat(30));
  
  if (workingAccounts.length > 0) {
    console.log(`\n✅ WORKING ACCOUNTS (${workingAccounts.length}):`);
    workingAccounts.forEach((account, i) => {
      console.log(`  ${i + 1}. ${account.email} / ${account.password}`);
    });
  } else {
    console.log('\n❌ NO WORKING ACCOUNTS FOUND');
  }
  
  if (failedAccounts.length > 0) {
    console.log(`\n❌ FAILED ACCOUNTS (${failedAccounts.length}):`);
    failedAccounts.forEach((account, i) => {
      console.log(`  ${i + 1}. ${account.email} - ${account.error || account.errors?.join(', ') || 'Login failed'}`);
    });
  }
  
  console.log('\n💡 RECOMMENDATIONS:');
  if (workingAccounts.length > 0) {
    console.log('- Use the working accounts for automated testing');
    console.log('- Update test files with correct credentials');
    console.log('- Run full bento grid test suite');
  } else {
    console.log('- Create test accounts manually in your auth system');
    console.log('- Check if email verification is required');
    console.log('- Test against production where auth might work better');
    console.log('- Consider using your real account for testing');
  }
  
  console.log('\n📸 Screenshots saved in test-results/ for working accounts');
}

// Run the account finder
findWorkingAccounts().catch(console.error);
