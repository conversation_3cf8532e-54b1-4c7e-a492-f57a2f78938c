// Apply RLS fix for team_members table
import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: 'client/.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyRLSFix() {
  console.log('🔧 Applying RLS fix for team_members table...\n');

  try {
    // Read the SQL fix file
    const sqlFix = readFileSync('fix-team-members-rls.sql', 'utf8');
    
    // Split into individual statements
    const statements = sqlFix
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Executing ${statements.length} SQL statements...\n`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`${i + 1}. ${statement.substring(0, 60)}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          console.log(`   ✅ Success`);
        }
      } catch (err) {
        console.log(`   ❌ Exception: ${err.message}`);
      }
    }

    console.log('\n🧪 Testing the fix...');
    
    // Test the team_members query
    const { data, error } = await supabase
      .from('team_members')
      .select('id, user_id, team_id, role')
      .limit(1);

    if (error) {
      console.log('❌ Test failed:', error.message);
    } else {
      console.log('✅ Test successful - team_members query working');
    }

  } catch (err) {
    console.error('❌ Failed to apply RLS fix:', err.message);
  }
}

// Run the fix
applyRLSFix().catch(console.error);
