// Bento Grid Navigation System Tests
const { test, expect } = require('@playwright/test');

/**
 * Comprehensive test suite for the Royaltea bento grid navigation system.
 * Tests each tile's routing, functionality, and visual elements with proper authentication.
 */

// Test configuration
const SITE_URL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173';
const PRODUCTION_URL = 'https://royalty.technology';

// Test user credentials (from existing test files)
const TEST_USERS = {
  admin: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  regular: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
};

// Helper function to authenticate user
async function authenticateUser(page, userType = 'regular') {
  const user = TEST_USERS[userType];
  console.log(`🔑 Authenticating as ${userType}: ${user.email}`);

  try {
    // Navigate to the site
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    // Check if login is needed
    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible({ timeout: 5000 });

    if (needsAuth) {
      console.log('📝 Login required, authenticating...');

      // Fill login form
      await emailInput.fill(user.email);
      await page.fill('input[type="password"]', user.password);

      // Submit login
      await page.click('button[type="submit"]');

      // Wait for authentication to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000);

      // Verify authentication succeeded
      const stillNeedsAuth = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
      if (stillNeedsAuth) {
        throw new Error(`Authentication failed for ${user.email}`);
      }

      console.log('✅ Authentication successful');
    } else {
      console.log('✅ Already authenticated or no auth required');
    }

    // Wait for the page to fully load after auth
    await page.waitForLoadState('networkidle');
    return true;

  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    return false;
  }
}

// Helper function to bypass authentication for local development
async function bypassAuthForLocal(page) {
  if (SITE_URL.includes('localhost')) {
    console.log('🔧 Setting up local auth bypass...');

    await page.addInitScript(() => {
      // Mock Supabase session for local testing
      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        token_type: 'bearer',
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test User'
          }
        }
      };

      // Set in localStorage (Supabase pattern)
      localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
      localStorage.setItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token', JSON.stringify(mockSession));

      // Set user context
      window.__TEST_USER__ = mockSession.user;
      window.__AUTHENTICATED__ = true;
    });
  }
}

test.describe('Bento Grid Navigation System', () => {
  // Test data for all bento grid tiles
  const bentoTiles = [
    // Row 1: Main Dashboard Area
    {
      name: 'Dashboard',
      canvasId: 'home',
      expectedRoute: '/',
      expectedTitle: /Dashboard|Royaltea/,
      expectedElements: ['Welcome back', 'Active Projects', 'Contributions'],
      icon: '🏠',
      size: 'large' // 2x2
    },
    
    // Row 1: Quick Action Tiles  
    {
      name: 'Start',
      canvasId: 'start',
      expectedRoute: '/start',
      expectedTitle: /Start/,
      expectedElements: ['Begin your journey', 'Start'],
      icon: '🚀',
      size: 'standard'
    },
    {
      name: 'Track',
      canvasId: 'track',
      expectedRoute: '/track',
      expectedTitle: /Track/,
      expectedElements: ['Track your work', 'Track'],
      icon: '⏱️',
      size: 'standard'
    },
    
    // Row 2: Full-width Earn Section
    {
      name: 'Earn',
      canvasId: 'earn',
      expectedRoute: '/earn',
      expectedTitle: /Earn/,
      expectedElements: ['Earn from your work', 'Revenue'],
      icon: '💰',
      size: 'wide' // 4x1
    },
    
    // Row 3: Management Tiles
    {
      name: 'Ventures',
      canvasId: 'projects',
      expectedRoute: '/projects',
      expectedTitle: /Projects|Ventures/,
      expectedElements: ['Venture management', 'Projects'],
      icon: '🚀',
      size: 'standard'
    },
    {
      name: 'Venture Wizard',
      canvasId: 'wizard',
      expectedRoute: '/project/wizard',
      expectedTitle: /Wizard|Create/,
      expectedElements: ['Create new ventures', 'Project'],
      icon: '🧙‍♂️',
      size: 'standard'
    },
    {
      name: 'Mission Board',
      canvasId: 'missions',
      expectedRoute: '/missions',
      expectedTitle: /Mission/,
      expectedElements: ['Mission Board', 'Discover'],
      icon: '🎯',
      size: 'standard'
    },
    
    // Row 4: Analytics & Validation
    {
      name: 'Analytics',
      canvasId: 'analytics',
      expectedRoute: '/analytics',
      expectedTitle: /Analytics/,
      expectedElements: ['Performance insights', 'Analytics'],
      icon: '📈',
      size: 'standard'
    },
    
    // Row 5: User & Social
    {
      name: 'Profile',
      canvasId: 'profile',
      expectedRoute: '/profile',
      expectedTitle: /Profile/,
      expectedElements: ['Profile', 'User'],
      icon: '👤',
      size: 'standard'
    },
    {
      name: 'Settings',
      canvasId: 'settings',
      expectedRoute: '/settings',
      expectedTitle: /Settings/,
      expectedElements: ['Configuration', 'Settings'],
      icon: '⚙️',
      size: 'standard'
    },
    
    // Row 6: Support & Admin
    {
      name: 'Learning',
      canvasId: 'learn',
      expectedRoute: '/learn',
      expectedTitle: /Learning|Learn/,
      expectedElements: ['Learning center', 'Tutorials'],
      icon: '🎓',
      size: 'standard'
    },
    {
      name: 'Admin',
      canvasId: 'admin',
      expectedRoute: '/admin',
      expectedTitle: /Admin/,
      expectedElements: ['Admin tools', 'Management'],
      icon: '🔧',
      size: 'standard',
      requiresAdmin: true
    }
  ];

  test.beforeEach(async ({ page }) => {
    // Set up authentication bypass for local development
    await bypassAuthForLocal(page);

    // Authenticate user
    const authSuccess = await authenticateUser(page, 'regular');
    if (!authSuccess) {
      test.skip('Authentication failed, skipping test');
      return;
    }

    // Navigate to home page where bento grid is displayed
    await page.goto(SITE_URL);
    await page.waitForLoadState('networkidle');

    // Wait for the bento grid to load with longer timeout for auth
    try {
      await page.waitForSelector('[data-canvas-card]', { timeout: 15000 });
    } catch (error) {
      console.log('⚠️  Bento grid not found, checking for alternative selectors...');

      // Try alternative selectors
      const alternatives = [
        '.grid', // Grid container
        '[data-testid="experimental-navigation"]', // Navigation container
        '.card', // Generic card selector
        '[role="gridcell"]' // Accessibility selector
      ];

      let found = false;
      for (const selector of alternatives) {
        if (await page.locator(selector).count() > 0) {
          console.log(`✅ Found alternative selector: ${selector}`);
          found = true;
          break;
        }
      }

      if (!found) {
        console.log('❌ No bento grid elements found');
        throw error;
      }
    }
  });

  test('should display all bento grid tiles', async ({ page }) => {
    // Check that the main bento grid container exists
    const bentoGrid = page.locator('.grid');
    await expect(bentoGrid).toBeVisible();
    
    // Check that canvas cards are present
    const canvasCards = page.locator('[data-canvas-card]');
    await expect(canvasCards).toHaveCount(await canvasCards.count());
    
    // Verify we have a reasonable number of tiles (at least 8)
    const cardCount = await canvasCards.count();
    expect(cardCount).toBeGreaterThanOrEqual(8);
    
    console.log(`Found ${cardCount} bento grid tiles`);
  });

  test('should have proper tile sizing and layout', async ({ page }) => {
    // Check for large dashboard tile (2x2)
    const dashboardTile = page.locator('[data-canvas-card]').filter({ hasText: 'Dashboard' });
    if (await dashboardTile.count() > 0) {
      await expect(dashboardTile).toHaveClass(/col-span-2.*row-span-2/);
    }
    
    // Check for wide earn tile (4x1)
    const earnTile = page.locator('[data-canvas-card]').filter({ hasText: 'Earn' });
    if (await earnTile.count() > 0) {
      await expect(earnTile).toHaveClass(/col-span-4/);
    }
  });

  test('should display tile icons and content', async ({ page }) => {
    const canvasCards = page.locator('[data-canvas-card]');
    const cardCount = await canvasCards.count();
    
    for (let i = 0; i < cardCount; i++) {
      const card = canvasCards.nth(i);
      
      // Each card should have an icon (emoji)
      const hasIcon = await card.locator('div').filter({ hasText: /[🏠🚀⏱️💰📊🎯✅📈🤖👤⚔️👥⚙️🔔🐛🎓❓🔧🖥️]/ }).count() > 0;
      expect(hasIcon).toBeTruthy();
      
      // Each card should have a title
      const hasTitle = await card.locator('h3').count() > 0;
      expect(hasTitle).toBeTruthy();
      
      // Each card should have a description
      const hasDescription = await card.locator('p').count() > 0;
      expect(hasDescription).toBeTruthy();
    }
  });

  test('should show hover effects on tiles', async ({ page }) => {
    const firstCard = page.locator('[data-canvas-card]').first();
    
    // Hover over the card
    await firstCard.hover();
    
    // Check for hover effects (scale, shadow, etc.)
    // Note: This might need adjustment based on actual CSS classes
    await page.waitForTimeout(500); // Allow time for hover animation
    
    // Verify hover state is applied
    const hasHoverEffect = await firstCard.evaluate(el => {
      const styles = window.getComputedStyle(el);
      return styles.transform !== 'none' || styles.boxShadow !== 'none';
    });
    
    expect(hasHoverEffect).toBeTruthy();
  });

  // Test each tile's navigation individually
  bentoTiles.forEach(tile => {
    test(`should navigate to ${tile.name} (${tile.expectedRoute})`, async ({ page }) => {
      // Skip admin tiles if not admin (we'll handle this in a separate test)
      if (tile.requiresAdmin) {
        test.skip();
        return;
      }
      
      // Find the tile by its content
      const tileElement = page.locator('[data-canvas-card]').filter({ hasText: tile.name });
      
      // If tile doesn't exist, skip (might be conditional)
      if (await tileElement.count() === 0) {
        console.log(`Tile "${tile.name}" not found, skipping...`);
        test.skip();
        return;
      }
      
      // Click the tile
      await tileElement.click();
      
      // Wait for navigation
      await page.waitForLoadState('networkidle');
      
      // Verify the URL changed to expected route
      const currentUrl = page.url();
      expect(currentUrl).toContain(tile.expectedRoute);
      
      // Verify page title or content
      if (tile.expectedTitle) {
        await expect(page).toHaveTitle(tile.expectedTitle);
      }
      
      // Check for expected elements on the page
      for (const element of tile.expectedElements) {
        const elementExists = await page.locator(`text=${element}`).count() > 0;
        if (!elementExists) {
          console.log(`Expected element "${element}" not found on ${tile.name} page`);
        }
      }
    });
  });

  test('should handle tile clicks without JavaScript errors', async ({ page }) => {
    // Listen for console errors
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Click several tiles
    const canvasCards = page.locator('[data-canvas-card]');
    const cardCount = Math.min(await canvasCards.count(), 5); // Test first 5 tiles
    
    for (let i = 0; i < cardCount; i++) {
      await page.goto('/'); // Reset to home
      await page.waitForLoadState('networkidle');
      
      const card = canvasCards.nth(i);
      await card.click();
      await page.waitForLoadState('networkidle');
      
      // Wait a moment for any async operations
      await page.waitForTimeout(1000);
    }
    
    // Check that no JavaScript errors occurred
    expect(errors.length).toBe(0);
    if (errors.length > 0) {
      console.log('JavaScript errors found:', errors);
    }
  });

  test('should maintain responsive layout on different screen sizes', async ({ page }) => {
    // Test desktop layout
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.reload();
    await page.waitForLoadState('networkidle');

    let canvasCards = page.locator('[data-canvas-card]');
    let desktopCount = await canvasCards.count();
    expect(desktopCount).toBeGreaterThan(0);

    // Test tablet layout
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await page.waitForLoadState('networkidle');

    canvasCards = page.locator('[data-canvas-card]');
    let tabletCount = await canvasCards.count();
    expect(tabletCount).toBeGreaterThan(0);

    // Test mobile layout
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');

    canvasCards = page.locator('[data-canvas-card]');
    let mobileCount = await canvasCards.count();
    expect(mobileCount).toBeGreaterThan(0);

    console.log(`Tile counts - Desktop: ${desktopCount}, Tablet: ${tabletCount}, Mobile: ${mobileCount}`);
  });

  test('should show context menu on long hover', async ({ page }) => {
    const firstCard = page.locator('[data-canvas-card]').first();

    // Hover for extended time to trigger context menu
    await firstCard.hover();
    await page.waitForTimeout(1200); // Wait longer than the 1000ms timeout

    // Check if context menu appears
    const contextMenu = page.locator('.absolute.top-full'); // Context menu selector
    if (await contextMenu.count() > 0) {
      await expect(contextMenu).toBeVisible();

      // Check for context menu items
      await expect(contextMenu.locator('text=Navigate')).toBeVisible();
      await expect(contextMenu.locator('text=Edit')).toBeVisible();
      await expect(contextMenu.locator('text=Info')).toBeVisible();
    }
  });

  test('should display connection count badges', async ({ page }) => {
    const canvasCards = page.locator('[data-canvas-card]');
    const cardCount = await canvasCards.count();

    for (let i = 0; i < Math.min(cardCount, 5); i++) {
      const card = canvasCards.nth(i);

      // Check for connection count badge
      const badge = card.locator('.absolute.bottom-3.left-3');
      if (await badge.count() > 0) {
        await expect(badge).toBeVisible();

        // Should contain "links" text
        await expect(badge).toContainText('links');
      }
    }
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Focus on the first card
    const firstCard = page.locator('[data-canvas-card]').first();
    await firstCard.focus();

    // Press Enter to navigate
    await page.keyboard.press('Enter');
    await page.waitForLoadState('networkidle');

    // Should have navigated away from home
    const currentUrl = page.url();
    expect(currentUrl).not.toBe('http://localhost:5173/');
  });

  test('should show active state for current canvas', async ({ page }) => {
    // Navigate to a specific page
    await page.goto('/start');
    await page.waitForLoadState('networkidle');

    // Go back to home to see the grid
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check if the Start tile shows active state
    const startTile = page.locator('[data-canvas-card]').filter({ hasText: 'Start' });
    if (await startTile.count() > 0) {
      // Look for active indicators (border, dot, etc.)
      const hasActiveIndicator = await startTile.locator('.absolute.inset-0.border-2').count() > 0 ||
                                 await startTile.locator('.absolute.top-3.right-3').count() > 0;

      // Note: This might not always be true depending on navigation state
      console.log('Active indicator found:', hasActiveIndicator);
    }
  });

  test('should load tile animations smoothly', async ({ page }) => {
    // Reload page to see animations
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Wait for animations to complete
    await page.waitForTimeout(2000);

    // Check that all tiles are visible and properly positioned
    const canvasCards = page.locator('[data-canvas-card]');
    const cardCount = await canvasCards.count();

    for (let i = 0; i < cardCount; i++) {
      const card = canvasCards.nth(i);
      await expect(card).toBeVisible();

      // Check that the card has proper opacity (animation completed)
      const opacity = await card.evaluate(el => window.getComputedStyle(el).opacity);
      expect(parseFloat(opacity)).toBeGreaterThan(0.9);
    }
  });

  test('should handle rapid tile clicking without errors', async ({ page }) => {
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });

    // Rapidly click different tiles
    const canvasCards = page.locator('[data-canvas-card]');
    const cardCount = Math.min(await canvasCards.count(), 3);

    for (let i = 0; i < cardCount; i++) {
      await page.goto('/'); // Reset
      await page.waitForLoadState('networkidle');

      const card = canvasCards.nth(i);
      await card.click();
      await page.waitForTimeout(100); // Very short wait
    }

    // Should handle rapid navigation without errors
    expect(errors.length).toBe(0);
  });
});

// Additional test suite for specific page functionality
test.describe('Bento Grid Page Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('Start page should load project creation tools', async ({ page }) => {
    const startTile = page.locator('[data-canvas-card]').filter({ hasText: 'Start' });
    if (await startTile.count() > 0) {
      await startTile.click();
      await page.waitForLoadState('networkidle');

      // Check for start page elements
      const hasStartContent = await page.locator('text=Start').count() > 0 ||
                             await page.locator('text=Begin').count() > 0 ||
                             await page.locator('text=Create').count() > 0;

      expect(hasStartContent).toBeTruthy();
    }
  });

  test('Track page should load work tracking interface', async ({ page }) => {
    const trackTile = page.locator('[data-canvas-card]').filter({ hasText: 'Track' });
    if (await trackTile.count() > 0) {
      await trackTile.click();
      await page.waitForLoadState('networkidle');

      // Check for track page elements
      const hasTrackContent = await page.locator('text=Track').count() > 0 ||
                             await page.locator('text=Work').count() > 0 ||
                             await page.locator('text=Time').count() > 0;

      expect(hasTrackContent).toBeTruthy();
    }
  });

  test('Earn page should load revenue interface', async ({ page }) => {
    const earnTile = page.locator('[data-canvas-card]').filter({ hasText: 'Earn' });
    if (await earnTile.count() > 0) {
      await earnTile.click();
      await page.waitForLoadState('networkidle');

      // Check for earn page elements
      const hasEarnContent = await page.locator('text=Earn').count() > 0 ||
                            await page.locator('text=Revenue').count() > 0 ||
                            await page.locator('text=Royalty').count() > 0;

      expect(hasEarnContent).toBeTruthy();
    }
  });

  test('Projects page should load venture management', async ({ page }) => {
    const projectsTile = page.locator('[data-canvas-card]').filter({ hasText: 'Ventures' });
    if (await projectsTile.count() > 0) {
      await projectsTile.click();
      await page.waitForLoadState('networkidle');

      // Check for projects page elements
      const hasProjectsContent = await page.locator('text=Project').count() > 0 ||
                                 await page.locator('text=Venture').count() > 0;

      expect(hasProjectsContent).toBeTruthy();
    }
  });

  test('Mission Board should load task interface', async ({ page }) => {
    const missionTile = page.locator('[data-canvas-card]').filter({ hasText: 'Mission' });
    if (await missionTile.count() > 0) {
      await missionTile.click();
      await page.waitForLoadState('networkidle');

      // Check for mission board elements
      const hasMissionContent = await page.locator('text=Mission').count() > 0 ||
                               await page.locator('text=Quest').count() > 0 ||
                               await page.locator('text=Task').count() > 0;

      expect(hasMissionContent).toBeTruthy();
    }
  });
});
