#!/usr/bin/env node

/**
 * Debug Navigation Test
 * 
 * Specifically designed to debug the ExperimentalNavigation component
 * and understand why the bento grid isn't showing up.
 */

const { chromium } = require('playwright');

async function debugNavigationTest() {
  console.log('🔍 Debug Navigation Test - ExperimentalNavigation Component');
  console.log('='.repeat(60));

  const browser = await chromium.launch({ 
    headless: false, // Show browser
    slowMo: 500 // Slow down for visibility
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🌐 Navigating to localhost:5173...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Take initial screenshot
    await page.screenshot({ path: 'test-results/debug-initial.png', fullPage: true });
    console.log('📸 Initial screenshot saved');

    // Check what's actually rendered
    console.log('\n🔍 Analyzing page structure...');
    
    // Check for main components
    const hasExperimentalNav = await page.locator('[data-testid="experimental-navigation"]').count() > 0;
    const hasMainContent = await page.locator('#main-content').count() > 0;
    const hasUserContext = await page.locator('[data-testid="user-context"]').count() > 0;
    
    console.log(`ExperimentalNavigation: ${hasExperimentalNav}`);
    console.log(`Main Content: ${hasMainContent}`);
    console.log(`User Context: ${hasUserContext}`);

    // Check for authentication state
    console.log('\n🔐 Checking authentication state...');
    const authState = await page.evaluate(() => {
      return {
        localStorage: {
          supabaseToken: localStorage.getItem('supabase.auth.token'),
          sbToken: localStorage.getItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token'),
        },
        window: {
          testUser: window.__TEST_USER__,
          authenticated: window.__AUTHENTICATED__,
        },
        cookies: document.cookie
      };
    });
    
    console.log('Auth state:', JSON.stringify(authState, null, 2));

    // Check for specific navigation elements
    console.log('\n🧭 Looking for navigation elements...');
    
    const navigationSelectors = [
      // ExperimentalNavigation specific
      '[data-testid="experimental-navigation"]',
      '.experimental-navigation',
      
      // Grid view elements
      '[data-canvas-card]',
      '.grid-view',
      '.bento-grid',
      
      // Canvas elements
      '.canvas-card',
      '[role="gridcell"]',
      
      // General grid layouts
      '.grid',
      '[class*="grid"]',
      
      // Card elements
      '.card',
      '[class*="card"]',
      
      // HeroUI components
      '[data-slot="base"]', // HeroUI Card base
      '[data-slot="body"]', // HeroUI CardBody
      
      // Motion/Framer elements
      '[data-framer-component]',
      '.motion-div'
    ];

    let foundElements = [];
    for (const selector of navigationSelectors) {
      const count = await page.locator(selector).count();
      if (count > 0) {
        foundElements.push({ selector, count });
        console.log(`✅ Found ${count} elements: ${selector}`);
      } else {
        console.log(`❌ Not found: ${selector}`);
      }
    }

    // If we found some elements, let's investigate them
    if (foundElements.length > 0) {
      console.log('\n🔬 Investigating found elements...');
      
      for (const { selector, count } of foundElements.slice(0, 3)) { // Check first 3 types
        console.log(`\n--- Analyzing ${selector} ---`);
        
        const elements = page.locator(selector);
        for (let i = 0; i < Math.min(count, 3); i++) { // Check first 3 of each type
          const element = elements.nth(i);
          
          try {
            const text = await element.textContent();
            const classes = await element.getAttribute('class');
            const isVisible = await element.isVisible();
            
            console.log(`  Element ${i + 1}:`);
            console.log(`    Visible: ${isVisible}`);
            console.log(`    Classes: ${classes}`);
            console.log(`    Text: ${text?.substring(0, 100)}...`);
            
            if (isVisible && text && text.trim()) {
              console.log(`    ✅ This looks like a valid navigation element!`);
            }
          } catch (error) {
            console.log(`    ❌ Error analyzing element: ${error.message}`);
          }
        }
      }
    }

    // Try to trigger different view modes
    console.log('\n🎛️  Testing view mode changes...');
    
    // Try pressing keys that might trigger view changes
    const testKeys = ['Escape', 'Tab', 'Space', 'Enter'];
    for (const key of testKeys) {
      console.log(`Testing key: ${key}`);
      await page.keyboard.press(key);
      await page.waitForTimeout(1000);
      
      // Check if anything changed
      const newElementCount = await page.locator('[data-canvas-card]').count();
      if (newElementCount > 0) {
        console.log(`✅ Key ${key} revealed ${newElementCount} canvas cards!`);
        break;
      }
    }

    // Try adding test mode parameters
    console.log('\n🧪 Testing with URL parameters...');
    const testUrls = [
      'http://localhost:5173?test_mode=true',
      'http://localhost:5173?skip_onboarding=true',
      'http://localhost:5173?test_dashboard=true',
      'http://localhost:5173?debug=true'
    ];

    for (const url of testUrls) {
      console.log(`Testing URL: ${url}`);
      await page.goto(url);
      await page.waitForLoadState('networkidle');
      
      const canvasCards = await page.locator('[data-canvas-card]').count();
      const gridElements = await page.locator('.grid').count();
      
      console.log(`  Canvas cards: ${canvasCards}`);
      console.log(`  Grid elements: ${gridElements}`);
      
      if (canvasCards > 0 || gridElements > 0) {
        console.log(`✅ URL parameter worked! Taking screenshot...`);
        await page.screenshot({ path: `test-results/debug-${url.split('?')[1]}.png`, fullPage: true });
        
        // Test clicking on elements
        if (canvasCards > 0) {
          console.log('🖱️  Testing canvas card clicks...');
          const firstCard = page.locator('[data-canvas-card]').first();
          const cardText = await firstCard.textContent();
          console.log(`Clicking on: ${cardText?.substring(0, 50)}...`);
          
          await firstCard.click();
          await page.waitForLoadState('networkidle');
          
          const newUrl = page.url();
          console.log(`Navigated to: ${newUrl}`);
          
          await page.screenshot({ path: `test-results/debug-after-click.png`, fullPage: true });
        }
        
        break; // Found working configuration
      }
    }

    // Try setting up mock user and reloading
    console.log('\n👤 Testing with mock user setup...');
    await page.goto('http://localhost:5173');
    
    await page.evaluate(() => {
      // Set up comprehensive mock user
      const mockUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        user_metadata: {
          full_name: 'Test User'
        }
      };
      
      const mockSession = {
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 3600,
        token_type: 'bearer',
        user: mockUser
      };
      
      // Set in localStorage
      localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
      localStorage.setItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token', JSON.stringify(mockSession));
      
      // Set global variables
      window.__TEST_USER__ = mockUser;
      window.__AUTHENTICATED__ = true;
      window.__CURRENT_USER__ = mockUser;
      
      // Try to trigger React context updates
      window.dispatchEvent(new Event('storage'));
      
      console.log('Mock user setup complete');
    });

    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000); // Give React time to update

    const finalCanvasCards = await page.locator('[data-canvas-card]').count();
    const finalGridElements = await page.locator('.grid').count();
    
    console.log(`\nFinal results with mock user:`);
    console.log(`  Canvas cards: ${finalCanvasCards}`);
    console.log(`  Grid elements: ${finalGridElements}`);
    
    if (finalCanvasCards > 0 || finalGridElements > 0) {
      console.log('✅ Mock user setup worked!');
      await page.screenshot({ path: 'test-results/debug-with-mock-user.png', fullPage: true });
    }

    // Keep browser open for manual inspection
    console.log('\n👀 Browser will stay open for 30 seconds for manual inspection...');
    console.log('Check the browser to see what\'s actually rendered!');
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
    await page.screenshot({ path: 'test-results/debug-error.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('\n✅ Debug test completed!');
    console.log('\n📋 Summary:');
    console.log('- Check test-results/ folder for screenshots');
    console.log('- Look for patterns in what elements were found');
    console.log('- Try the URL parameters that worked');
  }
}

// Run the debug test
debugNavigationTest().catch(console.error);
