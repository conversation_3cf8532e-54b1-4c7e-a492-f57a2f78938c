// Simple approach to fix RLS by temporarily disabling it for team_members
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: 'client/.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function simpleRLSFix() {
  console.log('🔧 Attempting simple RLS fix...\n');

  // Test 1: Try to query team_members with RLS disabled approach
  console.log('1️⃣ Testing team_members query with service role key...');
  
  try {
    const { data, error } = await supabase
      .from('team_members')
      .select('id, user_id, team_id, role, is_admin')
      .limit(5);

    if (error) {
      console.log('❌ Service role query failed:', error.message);
      
      // If service role fails, suggest manual fix
      console.log('\n📋 Manual fix required:');
      console.log('1. Go to Supabase Dashboard > SQL Editor');
      console.log('2. Run this SQL:');
      console.log('   ALTER TABLE public.team_members DISABLE ROW LEVEL SECURITY;');
      console.log('3. Test the application');
      console.log('4. If working, re-enable with simpler policies');
      
    } else {
      console.log('✅ Service role query successful:', data?.length || 0, 'team members found');
      
      // Test with regular user context
      console.log('\n2️⃣ Testing with user context...');
      const testUserId = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b';
      
      const { data: userData, error: userError } = await supabase
        .from('team_members')
        .select('id, user_id, team_id, role')
        .eq('user_id', testUserId);

      if (userError) {
        console.log('❌ User context query failed:', userError.message);
      } else {
        console.log('✅ User context query successful:', userData?.length || 0, 'memberships found');
      }
    }
  } catch (err) {
    console.log('❌ Exception:', err.message);
  }

  // Test 2: Check if we can query teams table directly
  console.log('\n3️⃣ Testing teams table query...');
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id, name, alliance_type, created_by')
      .limit(5);

    if (error) {
      console.log('❌ Teams query failed:', error.message);
    } else {
      console.log('✅ Teams query successful:', data?.length || 0, 'teams found');
    }
  } catch (err) {
    console.log('❌ Teams query exception:', err.message);
  }

  console.log('\n💡 Recommendations:');
  console.log('1. If you have Supabase Dashboard access, disable RLS on team_members temporarily');
  console.log('2. Update the frontend to use a different query pattern');
  console.log('3. Consider using Supabase functions for complex queries');
}

// Run the test
simpleRLSFix().catch(console.error);
