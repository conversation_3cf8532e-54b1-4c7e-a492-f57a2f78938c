import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardBody, 
  Drawer, 
  DrawerContent, 
  DrawerHeader, 
  DrawerBody,
  Chip,
  Badge
} from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';
import useCanvasDefinitions from '../../hooks/useCanvasDefinitions';

/**
 * Enhanced Mobile Navigation Component
 * 
 * Provides comprehensive mobile navigation with:
 * - Touch gesture support
 * - Responsive design patterns
 * - Integration with NavigationContext
 * - Accessibility features
 * - Performance optimizations
 */

const EnhancedMobileNavigation = ({ 
  currentUser, 
  currentCanvas, 
  onCanvasChange,
  onViewModeChange,
  className = "" 
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [showGestureHints, setShowGestureHints] = useState(false);
  
  const location = useLocation();
  const navigate = useNavigate();
  const { 
    isMobile, 
    isTablet, 
    preferences, 
    navigationHistory, 
    favoriteCanvases,
    actions 
  } = useNavigation();
  
  const navigationCanvases = useCanvasDefinitions(currentUser, false);

  // Show gesture hints for new users
  useEffect(() => {
    const hasSeenHints = localStorage.getItem('mobile_gesture_hints_seen');
    if (!hasSeenHints && isMobile) {
      setShowGestureHints(true);
      setTimeout(() => {
        setShowGestureHints(false);
        localStorage.setItem('mobile_gesture_hints_seen', 'true');
      }, 5000);
    }
  }, [isMobile]);

  // Primary navigation items based on Start-Track-Earn journey
  const primaryNavItems = [
    {
      key: 'home',
      title: 'Dashboard',
      icon: '🏠',
      description: 'Your central hub',
      route: '/',
      color: 'primary'
    },
    {
      key: 'start',
      title: 'Start',
      icon: '🚀',
      description: 'Create projects',
      route: '/start',
      color: 'success'
    },
    {
      key: 'track',
      title: 'Track',
      icon: '⏱️',
      description: 'Log contributions',
      route: '/track',
      color: 'warning'
    },
    {
      key: 'earn',
      title: 'Earn',
      icon: '💰',
      description: 'View earnings',
      route: '/earn',
      color: 'secondary'
    }
  ];

  // Secondary navigation items
  const secondaryNavItems = [
    { key: 'projects', title: 'Projects', icon: '📁', route: '/projects' },
    { key: 'missions', title: 'Missions', icon: '🎯', route: '/missions' },
    { key: 'teams', title: 'Teams', icon: '👥', route: '/teams' },
    { key: 'analytics', title: 'Analytics', icon: '📊', route: '/analytics/contributions' },
    { key: 'profile', title: 'Profile', icon: '👤', route: '/profile' },
    { key: 'settings', title: 'Settings', icon: '⚙️', route: '/settings' }
  ];

  // Touch gesture handlers with improved sensitivity
  const handleTouchStart = useCallback((e) => {
    if (!preferences.touchGesturesEnabled) return;
    
    setTouchEnd(null);
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
      timestamp: Date.now()
    });
  }, [preferences.touchGesturesEnabled]);

  const handleTouchMove = useCallback((e) => {
    if (!touchStart || !preferences.touchGesturesEnabled) return;
    
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    });
  }, [touchStart, preferences.touchGesturesEnabled]);

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd || !preferences.touchGesturesEnabled) return;
    
    const deltaX = touchStart.x - touchEnd.x;
    const deltaY = touchStart.y - touchEnd.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const duration = Date.now() - touchStart.timestamp;
    
    // Minimum distance and maximum duration for gesture recognition
    if (distance < 50 || duration > 500) return;
    
    const isLeftSwipe = deltaX > 50;
    const isRightSwipe = deltaX < -50;
    const isUpSwipe = deltaY > 50;
    const isDownSwipe = deltaY < -50;

    // Handle gestures
    if (isRightSwipe && touchStart.x < 50) {
      // Swipe right from left edge - open drawer
      setIsDrawerOpen(true);
      actions.updateInteraction();
    } else if (isLeftSwipe) {
      // Swipe left - next section
      navigateToNext();
    } else if (isRightSwipe) {
      // Swipe right - previous section
      navigateToPrevious();
    } else if (isUpSwipe) {
      // Swipe up - show grid view
      onViewModeChange?.('grid');
      actions.updateInteraction();
    } else if (isDownSwipe) {
      // Swipe down - show content view
      onViewModeChange?.('content');
      actions.updateInteraction();
    }
  }, [touchStart, touchEnd, preferences.touchGesturesEnabled, actions, onViewModeChange]);

  // Navigation helpers
  const navigateToNext = useCallback(() => {
    const currentIndex = primaryNavItems.findIndex(item => 
      location.pathname.startsWith(item.route)
    );
    
    if (currentIndex < primaryNavItems.length - 1) {
      const nextItem = primaryNavItems[currentIndex + 1];
      navigate(nextItem.route);
      onCanvasChange?.(nextItem.key);
      actions.navigateToCanvas(nextItem.key, { method: 'swipe_left', route: nextItem.route });
    }
  }, [location.pathname, navigate, onCanvasChange, actions, primaryNavItems]);

  const navigateToPrevious = useCallback(() => {
    const currentIndex = primaryNavItems.findIndex(item => 
      location.pathname.startsWith(item.route)
    );
    
    if (currentIndex > 0) {
      const prevItem = primaryNavItems[currentIndex - 1];
      navigate(prevItem.route);
      onCanvasChange?.(prevItem.key);
      actions.navigateToCanvas(prevItem.key, { method: 'swipe_right', route: prevItem.route });
    }
  }, [location.pathname, navigate, onCanvasChange, actions, primaryNavItems]);

  // Get current navigation item
  const getCurrentItem = useCallback(() => {
    return primaryNavItems.find(item => 
      location.pathname.startsWith(item.route)
    ) || primaryNavItems[0];
  }, [location.pathname, primaryNavItems]);

  const currentItem = getCurrentItem();

  // Don't render if not mobile/tablet
  if (!isMobile && !isTablet) {
    return null;
  }

  return (
    <div className={className}>
      {/* Mobile Header */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-background/95 backdrop-blur-md border-b border-divider">
        <div className="flex items-center justify-between p-4">
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onClick={() => setIsDrawerOpen(true)}
            aria-label="Open navigation menu"
          >
            ☰
          </Button>
          
          <div className="flex items-center gap-2">
            <span className="text-xl">{currentItem.icon}</span>
            <span className="font-semibold text-sm">{currentItem.title}</span>
            {favoriteCanvases.includes(currentCanvas) && (
              <Badge content="★" size="sm" color="warning" />
            )}
          </div>
          
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onClick={() => {
              navigate('/profile');
              onCanvasChange?.('profile');
            }}
            aria-label="Go to profile"
          >
            👤
          </Button>
        </div>
      </div>

      {/* Mobile Content Area with Touch Gestures */}
      <div
        className="pt-16 min-h-screen"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Navigation Indicators */}
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-30">
          <div className="flex items-center gap-2 bg-background/90 backdrop-blur-md rounded-full px-4 py-2 border border-divider shadow-lg">
            {primaryNavItems.map((item, index) => (
              <motion.button
                key={item.key}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${
                  item.key === currentItem.key 
                    ? 'bg-primary w-6' 
                    : 'bg-default-300 hover:bg-default-400'
                }`}
                onClick={() => {
                  navigate(item.route);
                  onCanvasChange?.(item.key);
                  actions.navigateToCanvas(item.key, { method: 'indicator_tap', route: item.route });
                }}
                whileTap={{ scale: 0.8 }}
                aria-label={`Navigate to ${item.title}`}
              />
            ))}
          </div>
        </div>

        {/* Quick Actions Bar */}
        <div className="fixed bottom-20 left-0 right-0 z-30 px-4">
          <Card className="bg-background/90 backdrop-blur-md border border-divider">
            <CardBody className="p-3">
              <div className="flex items-center justify-around">
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => {
                    navigate('/project/wizard');
                    onCanvasChange?.('wizard');
                  }}
                  className="flex-1 mx-1"
                >
                  ➕ Create
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => {
                    navigate('/contributions');
                    onCanvasChange?.('contributions');
                  }}
                  className="flex-1 mx-1"
                >
                  ⏱️ Track
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => {
                    navigate('/missions');
                    onCanvasChange?.('missions');
                  }}
                  className="flex-1 mx-1"
                >
                  🎯 Missions
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Navigation Drawer */}
      <Drawer 
        isOpen={isDrawerOpen} 
        onClose={() => setIsDrawerOpen(false)}
        placement="left"
        size="sm"
        backdrop="blur"
      >
        <DrawerContent>
          <DrawerHeader>
            <div className="flex items-center gap-2">
              <span className="text-2xl">👑</span>
              <span className="font-bold">Royaltea</span>
            </div>
          </DrawerHeader>
          <DrawerBody>
            <div className="space-y-6">
              {/* Primary Navigation */}
              <div>
                <h3 className="text-sm font-medium text-foreground-600 mb-3">
                  Main Journey
                </h3>
                <div className="space-y-2">
                  {primaryNavItems.map((item) => (
                    <motion.div
                      key={item.key}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant={currentItem.key === item.key ? 'flat' : 'light'}
                        color={item.color}
                        onClick={() => {
                          navigate(item.route);
                          onCanvasChange?.(item.key);
                          setIsDrawerOpen(false);
                          actions.navigateToCanvas(item.key, { method: 'drawer_nav', route: item.route });
                        }}
                        className="w-full justify-start"
                      >
                        <span className="text-lg mr-3">{item.icon}</span>
                        <div className="text-left">
                          <div className="font-medium">{item.title}</div>
                          <div className="text-xs opacity-70">{item.description}</div>
                        </div>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Secondary Navigation */}
              <div>
                <h3 className="text-sm font-medium text-foreground-600 mb-3">
                  More Options
                </h3>
                <div className="space-y-2">
                  {secondaryNavItems.map((item) => (
                    <Button
                      key={item.key}
                      variant="light"
                      onClick={() => {
                        navigate(item.route);
                        onCanvasChange?.(item.key);
                        setIsDrawerOpen(false);
                        actions.navigateToCanvas(item.key, { method: 'drawer_secondary', route: item.route });
                      }}
                      className="w-full justify-start"
                    >
                      <span className="text-lg mr-3">{item.icon}</span>
                      {item.title}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Recent Navigation */}
              {navigationHistory.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-foreground-600 mb-3">
                    Recent
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {navigationHistory.slice(-5).map((entry, index) => {
                      const canvas = navigationCanvases[entry.to];
                      if (!canvas) return null;
                      
                      return (
                        <Chip
                          key={`${entry.to}-${index}`}
                          size="sm"
                          variant="flat"
                          onClick={() => {
                            // Resolve route parameters if needed
                            let resolvedRoute = canvas.route;

                            // Check if route has parameters that need to be resolved
                            if (resolvedRoute.includes(':id') || resolvedRoute.includes(':projectId')) {
                              const currentPath = location.pathname;

                              // Extract ID from current path if it matches a project route pattern
                              const projectIdMatch = currentPath.match(/\/project\/([^\/]+)/);
                              if (projectIdMatch && projectIdMatch[1]) {
                                const projectId = projectIdMatch[1];
                                resolvedRoute = resolvedRoute
                                  .replace(':id', projectId)
                                  .replace(':projectId', projectId);
                              } else {
                                // If no project ID is available, navigate to a safe default
                                if (entry.to === 'kanban' || entry.to === 'agreements') {
                                  resolvedRoute = '/projects';
                                } else if (entry.to === 'royalty' || entry.to === 'escrow') {
                                  resolvedRoute = '/revenue';
                                } else {
                                  return; // Skip navigation
                                }
                              }
                            }

                            navigate(resolvedRoute);
                            onCanvasChange?.(entry.to);
                            setIsDrawerOpen(false);
                          }}
                          className="cursor-pointer"
                        >
                          {canvas.icon} {canvas.title}
                        </Chip>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* Touch Gesture Hints */}
      <AnimatePresence>
        {showGestureHints && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed top-20 left-4 right-4 z-50 pointer-events-none"
          >
            <Card className="bg-primary/10 border border-primary/20">
              <CardBody className="p-3">
                <div className="text-center text-sm text-primary">
                  <div className="font-medium mb-1">Touch Gestures</div>
                  <div className="text-xs space-y-1">
                    <div>← Swipe from edge to open menu</div>
                    <div>↕️ Swipe up/down to change view</div>
                    <div>← → Swipe to navigate sections</div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedMobileNavigation;
