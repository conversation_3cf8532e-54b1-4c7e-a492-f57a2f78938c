// Global Authentication Setup for Playwright Tests
const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Test configuration
const SITE_URL = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173';

// Test user credentials
const TEST_USERS = {
  regular: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    stateFile: 'test-results/auth-state.json'
  },
  admin: {
    email: '<EMAIL>', 
    password: 'TestPassword123!',
    stateFile: 'test-results/admin-auth-state.json'
  }
};

async function globalSetup() {
  console.log('🔧 Setting up global authentication for Playwright tests...');
  
  // Ensure test-results directory exists
  const testResultsDir = path.join(process.cwd(), 'test-results');
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
  }

  // Set up authentication for each user type
  for (const [userType, user] of Object.entries(TEST_USERS)) {
    console.log(`🔑 Setting up authentication for ${userType} user...`);
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();

    try {
      // Navigate to the site
      await page.goto(SITE_URL);
      await page.waitForLoadState('networkidle');

      // Check if we're running locally and can bypass auth
      if (SITE_URL.includes('localhost')) {
        console.log(`🔧 Local development detected, setting up auth bypass for ${userType}...`);
        
        // Set up mock authentication for local development
        await page.addInitScript(() => {
          const mockSession = {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            expires_in: 3600,
            token_type: 'bearer',
            user: {
              id: `test-${userType}-id`,
              email: user.email,
              user_metadata: {
                full_name: `Test ${userType} User`
              }
            }
          };
          
          // Set in localStorage (Supabase pattern)
          localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
          localStorage.setItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token', JSON.stringify(mockSession));
          
          // Set user context
          window.__TEST_USER__ = mockSession.user;
          window.__AUTHENTICATED__ = true;
        });

        // Reload to apply the auth state
        await page.reload();
        await page.waitForLoadState('networkidle');
        
      } else {
        // Production authentication flow
        console.log(`🌐 Production environment detected, performing real authentication for ${userType}...`);
        
        // Check if login is needed
        const emailInput = page.locator('input[type="email"]').first();
        const needsAuth = await emailInput.isVisible({ timeout: 5000 });

        if (needsAuth) {
          console.log(`📝 Logging in ${userType} user...`);
          
          // Fill login form
          await emailInput.fill(user.email);
          await page.fill('input[type="password"]', user.password);
          
          // Submit login
          await page.click('button[type="submit"]');
          
          // Wait for authentication to complete
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(5000);
          
          // Verify authentication succeeded
          const stillNeedsAuth = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
          if (stillNeedsAuth) {
            throw new Error(`Authentication failed for ${userType} user: ${user.email}`);
          }
          
          console.log(`✅ ${userType} authentication successful`);
        } else {
          console.log(`✅ ${userType} already authenticated or no auth required`);
        }
      }

      // Save authentication state
      await context.storageState({ path: user.stateFile });
      console.log(`💾 Saved ${userType} auth state to ${user.stateFile}`);

    } catch (error) {
      console.error(`❌ Failed to set up authentication for ${userType}:`, error.message);
      
      // Create a minimal auth state file to prevent test failures
      const minimalState = {
        cookies: [],
        origins: [{
          origin: SITE_URL,
          localStorage: [
            {
              name: 'supabase.auth.token',
              value: JSON.stringify({
                access_token: 'mock-token',
                user: { id: `test-${userType}-id`, email: user.email }
              })
            }
          ]
        }]
      };
      
      fs.writeFileSync(user.stateFile, JSON.stringify(minimalState, null, 2));
      console.log(`⚠️  Created fallback auth state for ${userType}`);
      
    } finally {
      await browser.close();
    }
  }

  console.log('✅ Global authentication setup complete!');
}

module.exports = globalSetup;
