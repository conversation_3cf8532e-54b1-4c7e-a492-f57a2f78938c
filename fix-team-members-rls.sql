-- Fix infinite recursion in team_members RLS policies
-- The issue is that the policies are querying the same table they're protecting

-- Temporarily disable <PERSON><PERSON> to fix the policies
ALTER TABLE public.team_members DISABLE ROW LEVEL SECURITY;

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Team members are viewable by team members" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be added by team admins" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be updated by team admins" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be deleted by team admins" ON public.team_members;

-- Create simpler, non-recursive policies

-- Allow users to view their own team memberships
CREATE POLICY "Users can view their own team memberships" ON public.team_members
    FOR SELECT
    USING (user_id = auth.uid());

-- Allow team creators to manage team members (using teams table instead of recursive query)
CREATE POLICY "Team creators can manage team members" ON public.team_members
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = team_members.team_id
            AND teams.created_by = auth.uid()
        )
    );

-- Allow team admins to manage team members (but avoid recursion by using a different approach)
-- We'll create a function to check admin status safely
CREATE OR REPLACE FUNCTION public.is_team_admin(team_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.team_members
        WHERE team_id = team_id_param
        AND user_id = user_id_param
        AND is_admin = true
    );
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.is_team_admin TO authenticated;

-- Create policy using the function (this avoids recursion)
CREATE POLICY "Team admins can manage team members" ON public.team_members
    FOR ALL
    USING (
        public.is_team_admin(team_id, auth.uid())
    );

-- Re-enable RLS
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- Test the fix
SELECT 'RLS policies fixed for team_members table' as status;
