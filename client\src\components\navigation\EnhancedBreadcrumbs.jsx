import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Chip, Breadcrumbs, BreadcrumbItem } from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';
import { useLocation } from 'react-router-dom';

/**
 * Enhanced Navigation Breadcrumbs Component
 *
 * Provides contextual breadcrumb navigation with:
 * - Smart path generation based on navigation history
 * - Responsive design for mobile/desktop
 * - Accessibility features
 * - Animation and visual feedback
 * - Integration with NavigationContext
 */
const EnhancedBreadcrumbs = ({
  currentCanvas,
  onNavigate,
  className = "",
  showHistory = false,
  maxItems = 5,
  compact = false
}) => {
  const { navigationHistory, isMobile, preferences } = useNavigation();
  const location = useLocation();

  // Enhanced canvas mapping with categories and relationships
  const canvasMap = {
    home: { title: 'Dashboard', icon: '🏠', route: '/', category: 'core' },
    start: { title: 'Start', icon: '🚀', route: '/start', category: 'journey' },
    track: { title: 'Track', icon: '⏱️', route: '/track', category: 'journey' },
    earn: { title: 'Earn', icon: '💰', route: '/earn', category: 'journey' },
    projects: { title: 'Projects', icon: '📁', route: '/projects', category: 'work' },
    wizard: { title: 'Project Wizard', icon: '🧙‍♂️', route: '/project/wizard', category: 'work' },
    kanban: { title: 'Task Board', icon: '📋', route: '/project/:id/tasks', category: 'work' },
    missions: { title: 'Mission Board', icon: '🎯', route: '/missions', category: 'work' },
    agreements: { title: 'Agreements', icon: '📄', route: '/project/:id/agreements', category: 'work' },
    contributions: { title: 'Contributions', icon: '⏱️', route: '/contributions', category: 'tracking' },
    validation: { title: 'Validation', icon: '✅', route: '/validation/metrics', category: 'tracking' },
    revenue: { title: 'Revenue', icon: '💰', route: '/revenue', category: 'financial' },
    royalty: { title: 'Royalty Calculator', icon: '🧮', route: '/project/:projectId/royalty-calculator', category: 'financial' },
    escrow: { title: 'Escrow', icon: '🏦', route: '/project/:projectId/revenue', category: 'financial' },
    analytics: { title: 'Analytics', icon: '📊', route: '/analytics/contributions', category: 'insights' },
    insights: { title: 'AI Insights', icon: '🤖', route: '/analytics/insights', category: 'insights' },
    profile: { title: 'Profile', icon: '👤', route: '/profile', category: 'user' },
    teams: { title: 'Teams', icon: '👥', route: '/teams', category: 'social' },
    social: { title: 'Social', icon: '💬', route: '/social', category: 'social' },
    settings: { title: 'Settings', icon: '⚙️', route: '/settings', category: 'system' },
    notifications: { title: 'Notifications', icon: '🔔', route: '/notifications', category: 'system' },
    bugs: { title: 'Bug Reports', icon: '🐛', route: '/bugs', category: 'system' },
    learn: { title: 'Learning', icon: '🎓', route: '/learn', category: 'help' },
    help: { title: 'Help Center', icon: '❓', route: '/help', category: 'help' },
    admin: { title: 'Admin', icon: '🔧', route: '/admin', category: 'admin' },
    system: { title: 'System', icon: '🖥️', route: '/admin/system', category: 'admin' }
  };

  // Generate smart breadcrumb path
  const generateBreadcrumbs = () => {
    const breadcrumbs = [];
    
    // Always start with Home unless we're already there
    if (currentCanvas !== 'home') {
      breadcrumbs.push({
        id: 'home',
        title: 'Dashboard',
        icon: '🏠',
        route: '/',
        isActive: false,
        category: 'core'
      });
    }
    
    // Add intermediate breadcrumbs based on navigation history if enabled
    if (showHistory && navigationHistory.length > 0) {
      const recentHistory = navigationHistory
        .slice(-3) // Last 3 navigation steps
        .filter(entry => entry.to !== currentCanvas && entry.to !== 'home')
        .map(entry => ({
          id: entry.to,
          ...canvasMap[entry.to],
          isActive: false,
          timestamp: entry.timestamp
        }))
        .filter(Boolean);
      
      breadcrumbs.push(...recentHistory);
    }
    
    // Add current canvas if not home
    if (currentCanvas && currentCanvas !== 'home') {
      const canvasInfo = canvasMap[currentCanvas];
      if (canvasInfo) {
        breadcrumbs.push({
          id: currentCanvas,
          ...canvasInfo,
          isActive: true
        });
      }
    }
    
    // Limit breadcrumbs to maxItems
    if (breadcrumbs.length > maxItems) {
      const firstItem = breadcrumbs[0];
      const lastItems = breadcrumbs.slice(-(maxItems - 2));
      return [
        firstItem,
        { id: 'ellipsis', title: '...', icon: '⋯', isEllipsis: true },
        ...lastItems
      ];
    }
    
    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Don't show if only one item or animations disabled
  if (breadcrumbs.length <= 1 || (preferences.reducedMotion && !compact)) {
    return null;
  }

  // Mobile compact view
  if (isMobile || compact) {
    const currentItem = breadcrumbs.find(b => b.isActive);
    if (!currentItem) return null;

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`flex items-center gap-2 ${className}`}
      >
        <Chip
          variant="flat"
          color="primary"
          size="sm"
          startContent={<span>{currentItem.icon}</span>}
        >
          {currentItem.title}
        </Chip>
        {breadcrumbs.length > 1 && (
          <Chip variant="bordered" size="sm">
            +{breadcrumbs.length - 1}
          </Chip>
        )}
      </motion.div>
    );
  }

  // Full desktop breadcrumb view
  return (
    <motion.nav
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex items-center gap-1 ${className}`}
      role="navigation"
      aria-label="Breadcrumb navigation"
    >
      <Breadcrumbs 
        size="sm"
        separator="/"
        className="text-foreground-600"
      >
        {breadcrumbs.map((crumb, index) => (
          <BreadcrumbItem 
            key={`${crumb.id}-${index}`}
            className={`
              ${crumb.isEllipsis 
                ? 'cursor-default' 
                : crumb.isActive 
                  ? 'text-foreground font-medium cursor-default' 
                  : 'text-foreground-600 hover:text-foreground cursor-pointer transition-colors'
              }
            `}
            onClick={crumb.isEllipsis || crumb.isActive ? undefined : () => {
              if (onNavigate) {
                // Handle route parameter resolution for breadcrumb navigation
                let targetRoute = crumb.route;
                if (targetRoute && (targetRoute.includes(':id') || targetRoute.includes(':projectId'))) {
                  const currentPath = location.pathname;
                  const projectIdMatch = currentPath.match(/\/project\/([^\/]+)/);
                  if (projectIdMatch && projectIdMatch[1]) {
                    const projectId = projectIdMatch[1];
                    targetRoute = targetRoute
                      .replace(':id', projectId)
                      .replace(':projectId', projectId);
                  } else {
                    // Navigate to safe default if no project ID available
                    if (crumb.id === 'kanban' || crumb.id === 'agreements') {
                      targetRoute = '/projects';
                    } else if (crumb.id === 'royalty' || crumb.id === 'escrow') {
                      targetRoute = '/revenue';
                    }
                  }
                }
                onNavigate(crumb.id, { resolvedRoute: targetRoute });
              }
            }}
            aria-current={crumb.isActive ? 'page' : undefined}
          >
            {crumb.isEllipsis ? (
              <span className="text-foreground-400 px-1" aria-label="More navigation items">
                {crumb.icon}
              </span>
            ) : (
              <motion.span
                className="flex items-center gap-1"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                whileHover={!crumb.isActive ? { scale: 1.05 } : {}}
              >
                <span className="text-sm">{crumb.icon}</span>
                <span className="text-sm">{crumb.title}</span>
              </motion.span>
            )}
          </BreadcrumbItem>
        ))}
      </Breadcrumbs>
    </motion.nav>
  );
};

export default EnhancedBreadcrumbs;
