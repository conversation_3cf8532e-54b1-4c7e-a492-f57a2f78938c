import React, { useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import BountyBoard from '../../components/bounty/BountyBoard';

/**
 * Bounty Page - Competitive Marketplace for High-Value Tasks
 * 
 * This page provides the complete bounty marketplace experience including:
 * - High-value bounty discovery and browsing
 * - Competitive application process with skill verification
 * - Portfolio and qualification requirements
 * - Milestone-based payment systems
 * - Professional bounty hunting and earnings tracking
 * 
 * Features:
 * - Marketplace layout with advanced filtering and search
 * - Real-time bounty data integration with backend APIs
 * - Complete bounty lifecycle management from posting to completion
 * - Professional networking and reputation system
 */
const BountyPage = () => {
  const { currentUser, loading } = useContext(UserContext);

  // Show loading state while authentication is being verified
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading bounty marketplace...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-6">🎯</div>
          <h2 className="text-2xl font-bold mb-4">Bounty Access Required</h2>
          <p className="text-default-600 mb-8 max-w-md mx-auto">
            Please log in to access the bounty marketplace and compete for high-value opportunities.
          </p>
          <button
            onClick={() => window.location.href = '/login'}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bounty-page min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-8 mb-8">
        <div className="container mx-auto px-6">
          <div className="flex items-center gap-4">
            <span className="text-5xl">🎯</span>
            <div>
              <h1 className="text-4xl font-bold mb-2">Bounty Marketplace</h1>
              <p className="text-orange-100 text-lg">
                Compete for high-value bounties and showcase your expertise
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <BountyBoard className="w-full" />
      </div>

      {/* Footer Info */}
      <div className="bg-default-50 dark:bg-default-900 py-8 mt-12">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl mb-2">💰</div>
              <h3 className="font-semibold mb-2">High-Value Opportunities</h3>
              <p className="text-sm text-default-600">
                Access premium bounties with competitive compensation and milestone payments
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">🏆</div>
              <h3 className="font-semibold mb-2">Skill Verification</h3>
              <p className="text-sm text-default-600">
                Showcase your expertise through portfolio requirements and skill assessments
              </p>
            </div>
            <div>
              <div className="text-3xl mb-2">⚡</div>
              <h3 className="font-semibold mb-2">Fast-Track Career</h3>
              <p className="text-sm text-default-600">
                Build your reputation and advance your career through successful bounty completions
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BountyPage;
