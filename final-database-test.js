// Final comprehensive test of all database fixes
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: 'client/.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test user ID
const TEST_USER_ID = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b';

async function runComprehensiveTest() {
  console.log('🎯 Final Database Fixes Verification\n');
  console.log('Testing all the queries that were previously failing...\n');

  let allTestsPassed = true;

  // Test 1: Projects with created_by (was failing with owner_id)
  console.log('1️⃣ Testing Projects Query (Dashboard)');
  try {
    const { count, error } = await supabase
      .from('projects')
      .select('*', { count: 'exact', head: true })
      .eq('created_by', TEST_USER_ID);

    if (error) {
      console.log('   ❌ FAILED:', error.message);
      allTestsPassed = false;
    } else {
      console.log('   ✅ SUCCESS: Projects count query working');
    }
  } catch (err) {
    console.log('   ❌ EXCEPTION:', err.message);
    allTestsPassed = false;
  }

  // Test 2: Revenue entries through project_contributors
  console.log('\n2️⃣ Testing Revenue Entries Query (Dashboard)');
  try {
    const { data, error } = await supabase
      .from('revenue_entries')
      .select(`
        amount,
        project_id,
        projects!inner(
          project_contributors!inner(user_id)
        )
      `)
      .eq('projects.project_contributors.user_id', TEST_USER_ID);

    if (error) {
      console.log('   ❌ FAILED:', error.message);
      allTestsPassed = false;
    } else {
      console.log('   ✅ SUCCESS: Revenue entries query working');
    }
  } catch (err) {
    console.log('   ❌ EXCEPTION:', err.message);
    allTestsPassed = false;
  }

  // Test 3: Team members query (was causing 500 errors)
  console.log('\n3️⃣ Testing Team Members Query (MyTeams page)');
  try {
    const { data: memberships, error: membershipError } = await supabase
      .from('team_members')
      .select('*')
      .eq('user_id', TEST_USER_ID);

    if (membershipError) {
      console.log('   ❌ FAILED:', membershipError.message);
      allTestsPassed = false;
    } else {
      console.log('   ✅ SUCCESS: Team memberships query working');
      
      // Test the full flow like in MyTeams component
      if (memberships && memberships.length > 0) {
        const teamIds = memberships.map(m => m.team_id);
        const { data: teamsData, error: teamsError } = await supabase
          .from('teams')
          .select(`
            id,
            name,
            description,
            is_business_entity,
            alliance_type,
            company_id,
            created_at
          `)
          .in('id', teamIds);

        if (teamsError) {
          console.log('   ❌ FAILED: Teams details query failed:', teamsError.message);
          allTestsPassed = false;
        } else {
          console.log('   ✅ SUCCESS: Full team query flow working');
        }
      }
    }
  } catch (err) {
    console.log('   ❌ EXCEPTION:', err.message);
    allTestsPassed = false;
  }

  // Test 4: Contributions query
  console.log('\n4️⃣ Testing Contributions Query (Dashboard)');
  try {
    const { count, error } = await supabase
      .from('contributions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', TEST_USER_ID)
      .eq('status', 'approved');

    if (error) {
      console.log('   ❌ FAILED:', error.message);
      allTestsPassed = false;
    } else {
      console.log('   ✅ SUCCESS: Contributions query working');
    }
  } catch (err) {
    console.log('   ❌ EXCEPTION:', err.message);
    allTestsPassed = false;
  }

  // Test 5: Test the new RLS helper function
  console.log('\n5️⃣ Testing RLS Helper Function');
  try {
    const { data, error } = await supabase
      .rpc('is_team_admin', { 
        team_id_param: '00000000-0000-0000-0000-000000000000', // dummy UUID
        user_id_param: TEST_USER_ID 
      });

    if (error) {
      console.log('   ❌ FAILED:', error.message);
      allTestsPassed = false;
    } else {
      console.log('   ✅ SUCCESS: RLS helper function working');
    }
  } catch (err) {
    console.log('   ❌ EXCEPTION:', err.message);
    allTestsPassed = false;
  }

  // Final summary
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('🎉 ALL TESTS PASSED! Database fixes are working correctly.');
    console.log('\n✅ Fixed Issues:');
    console.log('   • Projects table: owner_id → created_by');
    console.log('   • Revenue entries: Direct user_id → project_contributors join');
    console.log('   • Team members: Infinite RLS recursion → Safe helper function');
    console.log('\n🚀 Your application should now load without 400/500 errors!');
  } else {
    console.log('❌ Some tests failed. Please check the errors above.');
  }
  console.log('='.repeat(50));
}

// Run the comprehensive test
runComprehensiveTest().catch(console.error);
