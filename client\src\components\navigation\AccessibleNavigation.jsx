import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Accessible Navigation Component
 * 
 * Provides comprehensive accessibility features for navigation:
 * - Screen reader support
 * - Keyboard navigation
 * - Focus management
 * - ARIA labels and landmarks
 * - High contrast support
 * - Reduced motion support
 */

const AccessibleNavigation = ({ 
  currentCanvas,
  canvases,
  onNavigate,
  onViewModeChange,
  className = ""
}) => {
  const { preferences, actions, keyboardUser } = useNavigation();
  const [focusedIndex, setFocusedIndex] = useState(0);
  const [announcements, setAnnouncements] = useState([]);
  const navigationRef = useRef(null);
  const canvasRefs = useRef({});

  // Canvas array for keyboard navigation
  const canvasArray = Object.values(canvases);

  // Announce navigation changes to screen readers
  const announce = (message) => {
    setAnnouncements(prev => [...prev, { id: Date.now(), message }]);
    
    // Remove announcement after it's been read
    setTimeout(() => {
      setAnnouncements(prev => prev.slice(1));
    }, 1000);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!preferences.keyboardNavigationEnabled) return;

      // Don't handle if user is typing in an input
      if (e.target.tagName === 'INPUT' || 
          e.target.tagName === 'TEXTAREA' || 
          e.target.contentEditable === 'true') {
        return;
      }

      switch (e.key) {
        case 'ArrowRight':
        case 'ArrowDown':
          e.preventDefault();
          setFocusedIndex(prev => {
            const newIndex = (prev + 1) % canvasArray.length;
            focusCanvas(newIndex);
            return newIndex;
          });
          break;

        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault();
          setFocusedIndex(prev => {
            const newIndex = prev === 0 ? canvasArray.length - 1 : prev - 1;
            focusCanvas(newIndex);
            return newIndex;
          });
          break;

        case 'Enter':
        case ' ':
          e.preventDefault();
          const focusedCanvas = canvasArray[focusedIndex];
          if (focusedCanvas) {
            onNavigate?.(focusedCanvas.id);
            announce(`Navigating to ${focusedCanvas.title}`);
            actions.updateInteraction();
          }
          break;

        case 'Home':
          e.preventDefault();
          setFocusedIndex(0);
          focusCanvas(0);
          break;

        case 'End':
          e.preventDefault();
          const lastIndex = canvasArray.length - 1;
          setFocusedIndex(lastIndex);
          focusCanvas(lastIndex);
          break;

        case 'Escape':
          e.preventDefault();
          onViewModeChange?.('grid');
          announce('Returned to grid view');
          break;

        // Quick navigation shortcuts
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
        case '7':
        case '8':
        case '9':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const index = parseInt(e.key) - 1;
            if (index < canvasArray.length) {
              const canvas = canvasArray[index];
              onNavigate?.(canvas.id);
              announce(`Quick navigation to ${canvas.title}`);
              actions.updateInteraction();
            }
          }
          break;

        // Help shortcut
        case '?':
          if (e.shiftKey) {
            e.preventDefault();
            showKeyboardHelp();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [focusedIndex, canvasArray, preferences.keyboardNavigationEnabled, onNavigate, onViewModeChange, actions]);

  // Focus management
  const focusCanvas = (index) => {
    const canvas = canvasArray[index];
    if (canvas && canvasRefs.current[canvas.id]) {
      canvasRefs.current[canvas.id].focus();
    }
  };

  // Show keyboard help
  const showKeyboardHelp = () => {
    const helpText = `
      Keyboard Navigation Help:
      - Arrow keys: Navigate between items
      - Enter/Space: Select item
      - Home/End: Go to first/last item
      - Escape: Return to grid view
      - Ctrl+1-9: Quick navigation
      - Shift+?: Show this help
    `;
    announce(helpText);
  };

  // Detect if user is using keyboard
  useEffect(() => {
    const handleKeyDown = () => {
      if (!keyboardUser) {
        actions.updatePreferences({ keyboardUser: true });
      }
    };

    const handleMouseDown = () => {
      if (keyboardUser) {
        actions.updatePreferences({ keyboardUser: false });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [keyboardUser, actions]);

  // Announce current location when canvas changes
  useEffect(() => {
    if (currentCanvas) {
      const canvas = canvases[currentCanvas];
      if (canvas) {
        announce(`Current location: ${canvas.title}. ${canvas.description}`);
      }
    }
  }, [currentCanvas, canvases]);

  return (
    <div 
      className={className}
      role="navigation"
      aria-label="Main navigation"
      ref={navigationRef}
    >
      {/* Screen reader announcements */}
      <div 
        aria-live="polite" 
        aria-atomic="true" 
        className="sr-only"
      >
        {announcements.map(announcement => (
          <div key={announcement.id}>
            {announcement.message}
          </div>
        ))}
      </div>

      {/* Skip link for screen readers */}
      <Button
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50"
        onClick={() => {
          const mainContent = document.querySelector('main');
          if (mainContent) {
            mainContent.focus();
          }
        }}
      >
        Skip to main content
      </Button>

      {/* Navigation instructions for screen readers */}
      <div className="sr-only">
        <h2>Navigation Instructions</h2>
        <p>
          Use arrow keys to navigate between sections. 
          Press Enter or Space to select. 
          Press Escape to return to overview.
          Press Shift+? for keyboard shortcuts.
        </p>
      </div>

      {/* Canvas grid with accessibility features */}
      <div 
        className="grid grid-cols-4 gap-4 auto-rows-fr"
        role="grid"
        aria-label="Navigation sections"
      >
        {canvasArray.map((canvas, index) => (
          <motion.div
            key={canvas.id}
            className={`relative ${canvas.id === 'home' ? 'col-span-2 row-span-2' : canvas.id === 'earn' ? 'col-span-4' : 'col-span-1'}`}
            role="gridcell"
            initial={preferences.reducedMotion ? {} : { opacity: 0, scale: 0.8 }}
            animate={preferences.reducedMotion ? {} : { opacity: 1, scale: 1 }}
            transition={preferences.reducedMotion ? {} : { delay: index * 0.1 }}
          >
            <Card
              ref={el => canvasRefs.current[canvas.id] = el}
              className={`
                h-full cursor-pointer transition-all duration-200
                ${focusedIndex === index ? 'ring-2 ring-primary ring-offset-2' : ''}
                ${currentCanvas === canvas.id ? 'ring-2 ring-secondary' : ''}
                focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
                hover:shadow-lg
              `}
              tabIndex={0}
              role="button"
              aria-label={`Navigate to ${canvas.title}. ${canvas.description}. ${canvas.connections.length} connections.`}
              aria-current={currentCanvas === canvas.id ? 'page' : undefined}
              onClick={() => {
                onNavigate?.(canvas.id);
                announce(`Navigating to ${canvas.title}`);
                actions.updateInteraction();
              }}
              onFocus={() => setFocusedIndex(index)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onNavigate?.(canvas.id);
                  announce(`Navigating to ${canvas.title}`);
                  actions.updateInteraction();
                }
              }}
            >
              <CardBody className={`
                p-0 relative overflow-hidden bg-gradient-to-br ${canvas.color}
                flex flex-col items-center justify-center text-white
                ${canvas.id === 'home' ? 'min-h-[200px]' : canvas.id === 'earn' ? 'min-h-[120px]' : 'min-h-[140px]'}
              `}>
                {/* High contrast mode support */}
                <div className="absolute inset-0 bg-black/0 contrast-more:bg-black/20" />
                
                {/* Content */}
                <div className="relative z-10 text-center p-4">
                  <div 
                    className={`${canvas.id === 'home' ? 'text-6xl mb-4' : canvas.id === 'earn' ? 'text-4xl mb-3' : 'text-3xl mb-2'}`}
                    aria-hidden="true"
                  >
                    {canvas.icon}
                  </div>

                  <h3 className={`font-bold ${canvas.id === 'home' ? 'text-2xl mb-2' : canvas.id === 'earn' ? 'text-xl mb-1' : 'text-lg mb-1'}`}>
                    {canvas.title}
                  </h3>

                  <p className={`opacity-90 ${canvas.id === 'home' ? 'text-base px-4' : 'text-sm px-2'}`}>
                    {canvas.description}
                  </p>

                  {/* Connection count for screen readers */}
                  <div className="sr-only">
                    {canvas.connections.length} connected sections
                  </div>
                </div>

                {/* Active indicator */}
                {currentCanvas === canvas.id && (
                  <div 
                    className="absolute top-3 right-3"
                    aria-label="Currently active section"
                  >
                    <div className="w-3 h-3 bg-white rounded-full" />
                  </div>
                )}

                {/* Keyboard shortcut indicator */}
                {index < 9 && (
                  <div 
                    className="absolute bottom-3 right-3 text-xs opacity-60"
                    aria-label={`Keyboard shortcut: Ctrl+${index + 1}`}
                  >
                    ⌘{index + 1}
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Keyboard navigation help */}
      {keyboardUser && (
        <div className="mt-4 text-center">
          <Button
            size="sm"
            variant="light"
            onClick={showKeyboardHelp}
            className="text-xs opacity-70 hover:opacity-100"
          >
            Press Shift+? for keyboard shortcuts
          </Button>
        </div>
      )}
    </div>
  );
};

export default AccessibleNavigation;
