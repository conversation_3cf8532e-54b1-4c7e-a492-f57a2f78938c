#!/usr/bin/env node

/**
 * Check App Loading
 * 
 * Diagnose why the React app isn't loading properly
 */

const { chromium } = require('playwright');

async function checkAppLoading() {
  console.log('🔍 Checking React App Loading');
  console.log('='.repeat(40));

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  // Capture console logs and errors
  const logs = [];
  const errors = [];
  
  page.on('console', msg => {
    logs.push(`${msg.type()}: ${msg.text()}`);
    console.log(`Console ${msg.type()}: ${msg.text()}`);
  });
  
  page.on('pageerror', error => {
    errors.push(error.message);
    console.error(`Page Error: ${error.message}`);
  });

  try {
    console.log('🌐 Loading page...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });

    // Wait a bit for React to load
    await page.waitForTimeout(3000);

    // Check basic HTML structure
    console.log('\n📄 Checking HTML structure...');
    const html = await page.content();
    console.log(`HTML length: ${html.length} characters`);
    
    // Check for React root
    const hasReactRoot = await page.locator('#root').count() > 0;
    console.log(`React root element: ${hasReactRoot}`);
    
    if (hasReactRoot) {
      const rootContent = await page.locator('#root').innerHTML();
      console.log(`Root content length: ${rootContent.length}`);
      console.log(`Root content preview: ${rootContent.substring(0, 200)}...`);
    }

    // Check for script tags
    const scriptTags = await page.locator('script').count();
    console.log(`Script tags found: ${scriptTags}`);

    // Check for CSS
    const styleTags = await page.locator('link[rel="stylesheet"], style').count();
    console.log(`Style tags found: ${styleTags}`);

    // Check network requests
    console.log('\n🌐 Checking network activity...');
    const responses = [];
    
    page.on('response', response => {
      responses.push({
        url: response.url(),
        status: response.status(),
        contentType: response.headers()['content-type']
      });
    });

    // Reload to capture network activity
    await page.reload({ waitUntil: 'networkidle' });
    await page.waitForTimeout(2000);

    console.log('\nNetwork responses:');
    responses.forEach(response => {
      console.log(`  ${response.status} ${response.url} (${response.contentType})`);
    });

    // Check if Vite is working
    console.log('\n⚡ Checking Vite development server...');
    const viteClient = responses.find(r => r.url.includes('@vite/client'));
    const viteHMR = responses.find(r => r.url.includes('vite') && r.url.includes('ws'));
    
    console.log(`Vite client loaded: ${!!viteClient}`);
    console.log(`Vite HMR connected: ${!!viteHMR}`);

    // Check for main.jsx or main.js
    const mainScript = responses.find(r => r.url.includes('main.jsx') || r.url.includes('main.js'));
    console.log(`Main script loaded: ${!!mainScript}`);

    // Try to evaluate React
    console.log('\n⚛️  Checking React...');
    const reactInfo = await page.evaluate(() => {
      return {
        React: typeof window.React,
        ReactDOM: typeof window.ReactDOM,
        reactDevTools: !!window.__REACT_DEVTOOLS_GLOBAL_HOOK__,
        viteHot: !!window.__vite_plugin_react_preamble_installed__
      };
    });
    
    console.log('React info:', reactInfo);

    // Check for any visible content
    console.log('\n👁️  Checking visible content...');
    const bodyText = await page.locator('body').textContent();
    console.log(`Body text length: ${bodyText?.length || 0}`);
    console.log(`Body text preview: ${bodyText?.substring(0, 200) || 'No text content'}...`);

    // Check for loading states
    const hasLoading = bodyText?.includes('Loading') || bodyText?.includes('loading');
    const hasError = bodyText?.includes('Error') || bodyText?.includes('error');
    
    console.log(`Has loading text: ${hasLoading}`);
    console.log(`Has error text: ${hasError}`);

    // Try to manually trigger React rendering
    console.log('\n🔧 Attempting manual React check...');
    const manualCheck = await page.evaluate(() => {
      // Check if React is available
      if (typeof window.React === 'undefined') {
        return { error: 'React not loaded' };
      }

      // Check if the app is mounted
      const root = document.getElementById('root');
      if (!root) {
        return { error: 'Root element not found' };
      }

      // Check if root has React fiber
      const hasFiber = root._reactInternalFiber || root._reactInternals;
      
      return {
        rootExists: !!root,
        rootHasChildren: root.children.length > 0,
        rootInnerHTML: root.innerHTML.substring(0, 100),
        hasFiber: !!hasFiber,
        reactVersion: window.React?.version
      };
    });

    console.log('Manual React check:', manualCheck);

    // Take a screenshot
    await page.screenshot({ path: 'test-results/app-loading-debug.png', fullPage: true });
    console.log('📸 Screenshot saved: test-results/app-loading-debug.png');

    // Summary
    console.log('\n📋 Summary:');
    console.log(`- Console logs: ${logs.length}`);
    console.log(`- JavaScript errors: ${errors.length}`);
    console.log(`- React root exists: ${hasReactRoot}`);
    console.log(`- Network requests: ${responses.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ JavaScript Errors:');
      errors.forEach(error => console.log(`  - ${error}`));
    }

    if (logs.length > 0) {
      console.log('\n📝 Recent Console Logs:');
      logs.slice(-10).forEach(log => console.log(`  ${log}`));
    }

    // Keep browser open for inspection
    console.log('\n👀 Browser staying open for 20 seconds...');
    await page.waitForTimeout(20000);

  } catch (error) {
    console.error('❌ App loading check failed:', error.message);
    await page.screenshot({ path: 'test-results/app-loading-error.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('\n✅ App loading check completed!');
  }
}

// Run the check
checkAppLoading().catch(console.error);
