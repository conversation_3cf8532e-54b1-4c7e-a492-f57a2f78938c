// Diagnostic Tests - What's Actually Being Rendered
const { test, expect } = require('@playwright/test');

test.describe('Diagnostic Tests', () => {
  test('should show what is actually rendered on home page', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Get the page title
    const title = await page.title();
    console.log('Page title:', title);
    
    // Get the body content
    const bodyText = await page.locator('body').textContent();
    console.log('Body text (first 500 chars):', bodyText.substring(0, 500));
    
    // Get all visible elements
    const visibleElements = await page.locator('*:visible').count();
    console.log('Number of visible elements:', visibleElements);
    
    // Check for specific elements
    const hasLogin = await page.locator('text=Login').count();
    const hasSignIn = await page.locator('text=Sign').count();
    const hasAuth = await page.locator('text=Auth').count();
    const hasError = await page.locator('text=Error').count();
    const hasLoading = await page.locator('text=Loading').count();
    
    console.log('Login elements:', hasLogin);
    console.log('Sign elements:', hasSignIn);
    console.log('Auth elements:', hasAuth);
    console.log('Error elements:', hasError);
    console.log('Loading elements:', hasLoading);
    
    // Check for React components
    const reactElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('[data-reactroot], [data-react-helmet]');
      return elements.length;
    });
    console.log('React elements found:', reactElements);
    
    // Check console errors
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Wait a bit more for any async errors
    await page.waitForTimeout(2000);
    
    if (errors.length > 0) {
      console.log('Console errors:', errors);
    }
    
    // The test should pass - we're just gathering info
    expect(true).toBe(true);
  });

  test('should show what is rendered on missions page', async ({ page }) => {
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Get the page content
    const bodyText = await page.locator('body').textContent();
    console.log('Missions page body text (first 500 chars):', bodyText.substring(0, 500));
    
    // Check for specific mission-related elements
    const hasMission = await page.locator('text=Mission').count();
    const hasBoard = await page.locator('text=Board').count();
    const hasTask = await page.locator('text=Task').count();
    const hasQuest = await page.locator('text=Quest').count();
    
    console.log('Mission text elements:', hasMission);
    console.log('Board text elements:', hasBoard);
    console.log('Task text elements:', hasTask);
    console.log('Quest text elements:', hasQuest);
    
    // Check for data-testid attributes
    const testIds = await page.evaluate(() => {
      const elements = document.querySelectorAll('[data-testid]');
      return Array.from(elements).map(el => el.getAttribute('data-testid'));
    });
    console.log('Available test IDs:', testIds);
    
    expect(true).toBe(true);
  });

  test('should check authentication state', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Check if user is authenticated
    const isAuthenticated = await page.evaluate(() => {
      // Check for common auth indicators
      return !!(
        localStorage.getItem('supabase.auth.token') ||
        sessionStorage.getItem('supabase.auth.token') ||
        document.cookie.includes('auth') ||
        window.supabase
      );
    });
    
    console.log('User appears to be authenticated:', isAuthenticated);
    
    // Check for auth-related elements
    const hasLoginButton = await page.locator('button:has-text("Login"), button:has-text("Sign")').count();
    const hasLogoutButton = await page.locator('button:has-text("Logout"), button:has-text("Sign Out")').count();
    const hasProfile = await page.locator('text=Profile').count();
    
    console.log('Login buttons found:', hasLoginButton);
    console.log('Logout buttons found:', hasLogoutButton);
    console.log('Profile elements found:', hasProfile);
    
    expect(true).toBe(true);
  });

  test('should check for JavaScript errors and network issues', async ({ page }) => {
    const errors = [];
    const networkErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('requestfailed', request => {
      networkErrors.push(`${request.method()} ${request.url()} - ${request.failure().errorText}`);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Navigate to a few pages to check for errors
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    await page.goto('/teams');
    await page.waitForLoadState('networkidle');
    
    console.log('JavaScript errors found:', errors.length);
    if (errors.length > 0) {
      console.log('Errors:', errors.slice(0, 5)); // Show first 5 errors
    }
    
    console.log('Network errors found:', networkErrors.length);
    if (networkErrors.length > 0) {
      console.log('Network errors:', networkErrors.slice(0, 5)); // Show first 5 errors
    }
    
    expect(true).toBe(true);
  });
});
