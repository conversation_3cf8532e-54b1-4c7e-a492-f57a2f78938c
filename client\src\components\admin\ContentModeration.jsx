import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Content Moderation Component - Content Review and Moderation Queue
 * 
 * Features:
 * - Content moderation queue with pending reviews
 * - Automated content filtering and flagging
 * - Community guidelines enforcement
 * - Appeal management and resolution
 * - Content action history and audit trail
 */
const ContentModeration = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [moderationQueue, setModerationQueue] = useState([]);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('pending');
  const [selectedItem, setSelectedItem] = useState(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewNotes, setReviewNotes] = useState('');

  // Load moderation queue
  const loadModerationQueue = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock moderation queue data for development
      const mockQueue = [
        {
          id: 'mod-1',
          contentType: 'project',
          contentId: 'proj-123',
          title: 'Alpha Development Project',
          flaggedBy: 'community',
          flagReason: 'inappropriate_language',
          flagDetails: 'Contains profanity in project description',
          content: 'This project will be f***ing amazing and will revolutionize the industry...',
          reportedBy: '<EMAIL>',
          status: 'pending',
          priority: 'medium',
          createdAt: '2025-01-16T10:30:00Z',
          author: {
            name: 'John Developer',
            email: '<EMAIL>',
            reputation: 4.2
          }
        },
        {
          id: 'mod-2',
          contentType: 'profile',
          contentId: 'user-456',
          title: 'User Profile Bio',
          flaggedBy: 'automated',
          flagReason: 'spam_links',
          flagDetails: 'Multiple external links detected in bio',
          content: 'Check out my amazing deals at www.suspicious-site.com and get 90% off! Also visit my other site...',
          reportedBy: 'system',
          status: 'pending',
          priority: 'high',
          createdAt: '2025-01-16T09:15:00Z',
          author: {
            name: 'Mike Johnson',
            email: '<EMAIL>',
            reputation: 2.8
          }
        },
        {
          id: 'mod-3',
          contentType: 'alliance',
          contentId: 'alliance-789',
          title: 'TechCorp Solutions Alliance',
          flaggedBy: 'user_report',
          flagReason: 'misleading_information',
          flagDetails: 'Claims to be affiliated with major tech companies without proof',
          content: 'We are officially partnered with Google, Microsoft, and Apple to deliver cutting-edge solutions...',
          reportedBy: '<EMAIL>',
          status: 'pending',
          priority: 'medium',
          createdAt: '2025-01-16T08:45:00Z',
          author: {
            name: 'TechCorp Admin',
            email: '<EMAIL>',
            reputation: 3.5
          }
        },
        {
          id: 'mod-4',
          contentType: 'comment',
          contentId: 'comment-321',
          title: 'Project Comment',
          flaggedBy: 'community',
          flagReason: 'harassment',
          flagDetails: 'Personal attack on project creator',
          content: 'This is the worst project I\'ve ever seen. The creator is clearly incompetent and should quit...',
          reportedBy: '<EMAIL>',
          status: 'pending',
          priority: 'high',
          createdAt: '2025-01-16T07:20:00Z',
          author: {
            name: 'Angry User',
            email: '<EMAIL>',
            reputation: 1.9
          }
        },
        {
          id: 'mod-5',
          contentType: 'project',
          contentId: 'proj-654',
          title: 'Crypto Investment Scheme',
          flaggedBy: 'automated',
          flagReason: 'potential_scam',
          flagDetails: 'Contains keywords associated with investment scams',
          content: 'Guaranteed 1000% returns in 30 days! Invest now and become rich! No risk involved...',
          reportedBy: 'system',
          status: 'reviewed',
          priority: 'critical',
          createdAt: '2025-01-15T16:30:00Z',
          reviewedAt: '2025-01-16T09:00:00Z',
          reviewedBy: 'Admin User',
          action: 'removed',
          reviewNotes: 'Clear investment scam. Content removed and user warned.',
          author: {
            name: 'Scammer Account',
            email: '<EMAIL>',
            reputation: 0.5
          }
        }
      ];

      setModerationQueue(mockQueue);
      
    } catch (error) {
      console.error('Error loading moderation queue:', error);
      toast.error('Failed to load moderation queue');
    } finally {
      setLoading(false);
    }
  };

  // Filter moderation queue
  const filteredQueue = moderationQueue.filter(item => {
    const matchesType = filterType === 'all' || item.contentType === filterType;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesType && matchesStatus;
  });

  // Get priority color
  const getPriorityColor = (priority) => {
    const colors = {
      'critical': 'danger',
      'high': 'warning',
      'medium': 'primary',
      'low': 'default'
    };
    return colors[priority] || 'default';
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'pending': 'warning',
      'reviewed': 'success',
      'appealed': 'secondary',
      'escalated': 'danger'
    };
    return colors[status] || 'default';
  };

  // Get flag reason display
  const getFlagReasonDisplay = (reason) => {
    const reasons = {
      'inappropriate_language': 'Inappropriate Language',
      'spam_links': 'Spam Links',
      'misleading_information': 'Misleading Information',
      'harassment': 'Harassment',
      'potential_scam': 'Potential Scam',
      'copyright_violation': 'Copyright Violation',
      'hate_speech': 'Hate Speech',
      'violence': 'Violence/Threats'
    };
    return reasons[reason] || reason;
  };

  // Handle review action
  const handleReviewAction = (item, action) => {
    setSelectedItem(item);
    setReviewAction(action);
    setReviewNotes('');
    setShowReviewModal(true);
  };

  // Execute review action
  const executeReviewAction = async () => {
    try {
      if (!reviewNotes.trim()) {
        toast.error('Please provide review notes');
        return;
      }

      // Mock API call - in production this would call the moderation API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update item status locally
      setModerationQueue(prevQueue => 
        prevQueue.map(item => 
          item.id === selectedItem.id 
            ? { 
                ...item, 
                status: 'reviewed',
                action: reviewAction,
                reviewNotes: reviewNotes,
                reviewedAt: new Date().toISOString(),
                reviewedBy: currentUser?.display_name || 'Admin'
              }
            : item
        )
      );

      toast.success(`Content ${reviewAction} successfully`);
      setShowReviewModal(false);
      setSelectedItem(null);
      setReviewAction('');
      setReviewNotes('');
      
    } catch (error) {
      console.error('Error executing review action:', error);
      toast.error('Failed to execute action');
    }
  };

  // Handle contact user
  const handleContactUser = (item) => {
    toast.info(`Message interface for ${item.author.name} coming soon`);
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  useEffect(() => {
    loadModerationQueue();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🔍</span>
              <div>
                <h2 className="text-2xl font-bold">Content Moderation</h2>
                <p className="text-default-600">Review and moderate flagged content</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-warning">
                {filteredQueue.filter(item => item.status === 'pending').length}
              </div>
              <div className="text-sm text-default-600">Pending Reviews</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <Select
              label="Content Type"
              selectedKeys={[filterType]}
              onSelectionChange={(keys) => setFilterType(Array.from(keys)[0])}
              className="w-48"
            >
              <SelectItem key="all">All Types</SelectItem>
              <SelectItem key="project">Projects</SelectItem>
              <SelectItem key="profile">Profiles</SelectItem>
              <SelectItem key="alliance">Alliances</SelectItem>
              <SelectItem key="comment">Comments</SelectItem>
            </Select>
            
            <Select
              label="Status"
              selectedKeys={[filterStatus]}
              onSelectionChange={(keys) => setFilterStatus(Array.from(keys)[0])}
              className="w-48"
            >
              <SelectItem key="all">All Status</SelectItem>
              <SelectItem key="pending">Pending</SelectItem>
              <SelectItem key="reviewed">Reviewed</SelectItem>
              <SelectItem key="appealed">Appealed</SelectItem>
              <SelectItem key="escalated">Escalated</SelectItem>
            </Select>
            
            <Button
              color="primary"
              variant="flat"
              onClick={loadModerationQueue}
              startContent={<span>🔄</span>}
            >
              Refresh
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Moderation Queue */}
      <div className="space-y-4">
        {filteredQueue.length === 0 ? (
          <Card>
            <CardBody className="text-center py-8">
              <div className="text-default-500">No items found for the selected filters</div>
            </CardBody>
          </Card>
        ) : (
          filteredQueue.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Badge
                        color={getPriorityColor(item.priority)}
                        content={item.priority}
                        size="sm"
                      >
                        <span className="text-2xl">🚩</span>
                      </Badge>
                      <div>
                        <h3 className="font-semibold text-lg">{item.title}</h3>
                        <div className="flex items-center gap-2 text-sm text-default-600">
                          <Chip size="sm" variant="flat">{item.contentType}</Chip>
                          <span>•</span>
                          <span>Flagged by: {item.flaggedBy}</span>
                          <span>•</span>
                          <span>{formatTimestamp(item.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    <Chip
                      color={getStatusColor(item.status)}
                      variant="flat"
                      size="sm"
                    >
                      {item.status}
                    </Chip>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="text-sm font-medium text-default-700">Reason</label>
                      <div className="text-sm text-default-600">
                        {getFlagReasonDisplay(item.flagReason)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Reported By</label>
                      <div className="text-sm text-default-600">{item.reportedBy}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Author</label>
                      <div className="text-sm text-default-600">
                        {item.author.name} ({item.author.email})
                        <br />
                        Reputation: {item.author.reputation}⭐
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Details</label>
                      <div className="text-sm text-default-600">{item.flagDetails}</div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="text-sm font-medium text-default-700">Flagged Content</label>
                    <div className="mt-1 p-3 bg-default-100 rounded-lg text-sm">
                      {item.content}
                    </div>
                  </div>

                  {item.status === 'reviewed' && (
                    <div className="mb-4 p-3 bg-success-50 rounded-lg">
                      <div className="text-sm font-medium text-success-700">Review Completed</div>
                      <div className="text-sm text-success-600">
                        Action: {item.action} • By: {item.reviewedBy} • {formatTimestamp(item.reviewedAt)}
                      </div>
                      <div className="text-sm text-success-600 mt-1">
                        Notes: {item.reviewNotes}
                      </div>
                    </div>
                  )}

                  {item.status === 'pending' && (
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        color="success"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'approved')}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        color="warning"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'edited')}
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'removed')}
                      >
                        Remove
                      </Button>
                      <Button
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClick={() => handleContactUser(item)}
                      >
                        Contact User
                      </Button>
                    </div>
                  )}
                </CardBody>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Review Action Modal */}
      <Modal 
        isOpen={showReviewModal} 
        onClose={() => setShowReviewModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Review Action: {reviewAction}</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <p>You are about to <strong>{reviewAction}</strong> the following content:</p>
                <div className="mt-2 p-3 bg-default-100 rounded-lg text-sm">
                  {selectedItem?.content}
                </div>
              </div>
              
              <Textarea
                label="Review Notes (required)"
                placeholder="Please provide detailed notes about your decision..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                minRows={4}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowReviewModal(false)}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={executeReviewAction}
              disabled={!reviewNotes.trim()}
            >
              Confirm {reviewAction}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ContentModeration;
