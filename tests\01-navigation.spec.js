// Navigation and Routing Tests
const { test, expect } = require('@playwright/test');

test.describe('Navigation System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load the home page successfully', async ({ page }) => {
    await expect(page).toHaveTitle(/Royaltea/);
    
    // Check for main navigation elements
    await expect(page.locator('[data-testid="experimental-navigation"]')).toBeVisible();
  });

  test('should navigate to missions page', async ({ page }) => {
    await page.goto('/missions');
    
    // Wait for the mission board to load
    await page.waitForLoadState('networkidle');
    
    // Check for mission board elements
    await expect(page.locator('text=Mission Board')).toBeVisible();
    await expect(page.locator('[data-testid="mission-board"]')).toBeVisible();
  });

  test('should navigate to teams/alliances page', async ({ page }) => {
    await page.goto('/teams');
    
    await page.waitForLoadState('networkidle');
    
    // Check for teams page elements
    await expect(page.locator('text=Teams')).toBeVisible();
  });

  test('should navigate to alliances page', async ({ page }) => {
    await page.goto('/alliances');
    
    await page.waitForLoadState('networkidle');
    
    // Check for alliance list elements
    await expect(page.locator('text=Alliance')).toBeVisible();
  });

  test('should navigate to skill verification page', async ({ page }) => {
    await page.goto('/vetting');
    
    await page.waitForLoadState('networkidle');
    
    // Check for vetting dashboard elements
    await expect(page.locator('text=Skill Verification')).toBeVisible();
  });

  test('should navigate to ventures page', async ({ page }) => {
    await page.goto('/ventures');
    
    await page.waitForLoadState('networkidle');
    
    // Check for venture page elements
    await expect(page.locator('text=Venture')).toBeVisible();
  });

  test('should navigate to quests page', async ({ page }) => {
    await page.goto('/quests');
    
    await page.waitForLoadState('networkidle');
    
    // Check for quest board elements
    await expect(page.locator('text=Quest')).toBeVisible();
  });

  test('should handle 404 pages gracefully', async ({ page }) => {
    await page.goto('/non-existent-page');
    
    // Should show 404 page or redirect to home
    const url = page.url();
    expect(url).toMatch(/(404|not-found|\/)/);
  });

  test('should have working back navigation', async ({ page }) => {
    // Start at home
    await page.goto('/');
    
    // Navigate to missions
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Go back
    await page.goBack();
    await page.waitForLoadState('networkidle');
    
    // Should be back at home
    expect(page.url()).toContain('/');
  });

  test('should maintain navigation state during page transitions', async ({ page }) => {
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Navigate to another page
    await page.goto('/teams');
    await page.waitForLoadState('networkidle');
    
    // Navigate back to missions
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Should load successfully without errors
    await expect(page.locator('text=Mission')).toBeVisible();
  });
});
