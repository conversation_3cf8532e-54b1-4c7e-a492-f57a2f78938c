/**
 * Tailwind CSS Plugin Patch
 * 
 * This module provides a compatibility layer for the tailwindcss/plugin package
 * to fix the ES module import issue with HeroUI.
 * 
 * The issue: HeroUI tries to import tailwindcss/plugin with `import plugin from 'tailwindcss/plugin'`
 * but the tailwindcss/plugin package exports the function as the main export, not as default.
 */

// Create a compatible tailwindcss plugin function
// The tailwindcss/plugin module exports a function that creates Tailwind CSS plugins
// Since we're not actually using Tailwind CSS plugins in the browser (they're build-time),
// we can provide a simple stub that matches the expected API

function plugin(pluginFunction, config = {}) {
  // This is a stub implementation that matches the tailwindcss/plugin API
  // In a real Tailwind build process, this would register the plugin
  // But in the browser, we just return a no-op
  return {
    handler: pluginFunction,
    config: config
  };
}

// Add the withOptions method that some plugins use
plugin.withOptions = function(optionsFunction, configFunction) {
  return function(options = {}) {
    return plugin(optionsFunction(options), configFunction ? configFunction(options) : {});
  };
};

// Export both named and default for maximum compatibility
export default plugin;
export { plugin };
