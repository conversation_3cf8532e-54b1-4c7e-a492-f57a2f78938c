#!/usr/bin/env node

/**
 * Bento Grid Navigation Test Runner
 * 
 * Comprehensive testing script for the Royaltea bento grid navigation system.
 * Runs Playwright tests and generates detailed reports.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Royaltea Bento Grid Navigation Test Suite');
console.log('='.repeat(50));

// Check if <PERSON><PERSON> is installed
try {
  execSync('npx playwright --version', { stdio: 'pipe' });
  console.log('✅ Playwright is installed');
} catch (error) {
  console.log('❌ Playwright not found. Installing...');
  execSync('npm install @playwright/test', { stdio: 'inherit' });
  execSync('npx playwright install', { stdio: 'inherit' });
}

// Check if the development server is running
async function checkDevServer() {
  try {
    const response = await fetch('http://localhost:5173');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Start development server if not running
async function ensureDevServer() {
  const isRunning = await checkDevServer();
  
  if (!isRunning) {
    console.log('🚀 Starting development server...');
    console.log('Please run: cd client && npm run dev');
    console.log('Then run this test script again.');
    process.exit(1);
  } else {
    console.log('✅ Development server is running');
  }
}

// Run the tests
async function runTests() {
  console.log('\n🧪 Running Bento Grid Navigation Tests with Authentication...');
  console.log('-'.repeat(50));

  try {
    // Determine which config to use
    const useAuthConfig = !args.includes('--no-auth');
    const configFile = useAuthConfig ? 'playwright-auth.config.js' : 'playwright.config.js';

    console.log(`📋 Using config: ${configFile}`);
    console.log(`🔐 Authentication: ${useAuthConfig ? 'Enabled' : 'Disabled'}`);

    // Build the command
    let command = `npx playwright test tests/bento-grid-navigation.spec.js --config=${configFile}`;

    // Add reporter
    command += ' --reporter=html';

    // Add project selection if specified
    if (process.env.PLAYWRIGHT_PROJECT) {
      command += ` --project=${process.env.PLAYWRIGHT_PROJECT}`;
    }

    // Add debug mode if specified
    if (process.env.PWDEBUG) {
      command += ' --debug';
    }

    // Add headed mode if specified
    if (process.env.PLAYWRIGHT_HEADED) {
      command += ' --headed';
    }

    console.log(`🚀 Running: ${command}`);

    // Run the tests
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: {
        ...process.env,
        PLAYWRIGHT_BASE_URL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173'
      }
    });

    console.log('\n✅ Tests completed successfully!');
    console.log('📊 View detailed report: npx playwright show-report');

  } catch (error) {
    console.log('\n❌ Some tests failed. Check the report for details.');
    console.log('📊 View detailed report: npx playwright show-report');
    console.log('\n🔍 Debugging tips:');
    console.log('- Run with --headed to see browser interactions');
    console.log('- Run with --debug to step through tests');
    console.log('- Check test-results/ folder for screenshots and videos');
    process.exit(1);
  }
}

// Generate test summary
function generateTestSummary() {
  const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
  
  if (fs.existsSync(resultsPath)) {
    try {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log('\n📋 Test Summary:');
      console.log('-'.repeat(30));
      console.log(`Total Tests: ${results.stats?.total || 'N/A'}`);
      console.log(`Passed: ${results.stats?.passed || 'N/A'}`);
      console.log(`Failed: ${results.stats?.failed || 'N/A'}`);
      console.log(`Skipped: ${results.stats?.skipped || 'N/A'}`);
      
      if (results.stats?.failed > 0) {
        console.log('\n❌ Failed Tests:');
        // Add logic to show failed test details
      }
      
    } catch (error) {
      console.log('⚠️  Could not parse test results');
    }
  }
}

// Main execution
async function main() {
  try {
    await ensureDevServer();
    await runTests();
    generateTestSummary();
    
    console.log('\n🎉 Bento Grid Navigation Testing Complete!');
    console.log('\nNext steps:');
    console.log('1. Review the HTML report: npx playwright show-report');
    console.log('2. Fix any failing tests');
    console.log('3. Run tests again to verify fixes');
    
  } catch (error) {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node test-bento-grid.js [options]

Options:
  --help, -h     Show this help message
  --debug        Run tests in debug mode
  --headed       Run tests in headed mode (show browser)
  --project      Specify browser project (chromium-auth, firefox-auth, mobile-chrome-auth, admin-tests)
  --no-auth      Disable authentication (for testing without login)
  --production   Test against production site (https://royalty.technology)
  --local        Test against local development server (default)

Examples:
  node test-bento-grid.js                           # Basic authenticated testing
  node test-bento-grid.js --headed                  # Show browser during tests
  node test-bento-grid.js --project=chromium-auth   # Test specific browser
  node test-bento-grid.js --debug                   # Step through tests
  node test-bento-grid.js --no-auth                 # Test without authentication
  node test-bento-grid.js --production              # Test production site
  node test-bento-grid.js --project=admin-tests     # Test admin-only features
  `);
  process.exit(0);
}

// Add debug mode
if (args.includes('--debug')) {
  process.env.PWDEBUG = '1';
}

// Add headed mode
if (args.includes('--headed')) {
  process.env.PLAYWRIGHT_HEADED = '1';
}

// Add project selection
const projectArg = args.find(arg => arg.startsWith('--project='));
if (projectArg) {
  process.env.PLAYWRIGHT_PROJECT = projectArg.split('=')[1];
}

// Set base URL based on arguments
if (args.includes('--production')) {
  process.env.PLAYWRIGHT_BASE_URL = 'https://royalty.technology';
  console.log('🌐 Testing against production site');
} else if (args.includes('--local')) {
  process.env.PLAYWRIGHT_BASE_URL = 'http://localhost:5173';
  console.log('🏠 Testing against local development server');
}

// Run the main function
main().catch(console.error);
