#!/usr/bin/env node

/**
 * Bento Grid Navigation Test Runner
 * 
 * Comprehensive testing script for the Royaltea bento grid navigation system.
 * Runs Playwright tests and generates detailed reports.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Royaltea Bento Grid Navigation Test Suite');
console.log('='.repeat(50));

// Check if <PERSON><PERSON> is installed
try {
  execSync('npx playwright --version', { stdio: 'pipe' });
  console.log('✅ Playwright is installed');
} catch (error) {
  console.log('❌ Playwright not found. Installing...');
  execSync('npm install @playwright/test', { stdio: 'inherit' });
  execSync('npx playwright install', { stdio: 'inherit' });
}

// Check if the development server is running
async function checkDevServer() {
  try {
    const response = await fetch('http://localhost:5173');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Start development server if not running
async function ensureDevServer() {
  const isRunning = await checkDevServer();
  
  if (!isRunning) {
    console.log('🚀 Starting development server...');
    console.log('Please run: cd client && npm run dev');
    console.log('Then run this test script again.');
    process.exit(1);
  } else {
    console.log('✅ Development server is running');
  }
}

// Run the tests
async function runTests() {
  console.log('\n🧪 Running Bento Grid Navigation Tests...');
  console.log('-'.repeat(50));
  
  try {
    // Run only the bento grid tests
    execSync('npx playwright test tests/bento-grid-navigation.spec.js --reporter=html', { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('\n✅ Tests completed successfully!');
    console.log('📊 View detailed report: npx playwright show-report');
    
  } catch (error) {
    console.log('\n❌ Some tests failed. Check the report for details.');
    console.log('📊 View detailed report: npx playwright show-report');
    process.exit(1);
  }
}

// Generate test summary
function generateTestSummary() {
  const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
  
  if (fs.existsSync(resultsPath)) {
    try {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      console.log('\n📋 Test Summary:');
      console.log('-'.repeat(30));
      console.log(`Total Tests: ${results.stats?.total || 'N/A'}`);
      console.log(`Passed: ${results.stats?.passed || 'N/A'}`);
      console.log(`Failed: ${results.stats?.failed || 'N/A'}`);
      console.log(`Skipped: ${results.stats?.skipped || 'N/A'}`);
      
      if (results.stats?.failed > 0) {
        console.log('\n❌ Failed Tests:');
        // Add logic to show failed test details
      }
      
    } catch (error) {
      console.log('⚠️  Could not parse test results');
    }
  }
}

// Main execution
async function main() {
  try {
    await ensureDevServer();
    await runTests();
    generateTestSummary();
    
    console.log('\n🎉 Bento Grid Navigation Testing Complete!');
    console.log('\nNext steps:');
    console.log('1. Review the HTML report: npx playwright show-report');
    console.log('2. Fix any failing tests');
    console.log('3. Run tests again to verify fixes');
    
  } catch (error) {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node test-bento-grid.js [options]

Options:
  --help, -h     Show this help message
  --debug        Run tests in debug mode
  --headed       Run tests in headed mode (show browser)
  --project      Specify browser project (chromium, firefox, webkit)

Examples:
  node test-bento-grid.js
  node test-bento-grid.js --headed
  node test-bento-grid.js --project=chromium
  `);
  process.exit(0);
}

// Add debug mode
if (args.includes('--debug')) {
  process.env.PWDEBUG = '1';
}

// Add headed mode
if (args.includes('--headed')) {
  process.env.PLAYWRIGHT_HEADED = '1';
}

// Add project selection
const projectArg = args.find(arg => arg.startsWith('--project='));
if (projectArg) {
  process.env.PLAYWRIGHT_PROJECT = projectArg.split('=')[1];
}

// Run the main function
main().catch(console.error);
