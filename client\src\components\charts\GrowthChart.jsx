import React from 'react';
import {
  Composed<PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON>
} from 'recharts';
import { <PERSON>, CardBody, CardHeader, Chip } from '@heroui/react';

/**
 * Growth Chart Component - Multi-metric Growth Visualization
 * 
 * Features:
 * - Combined bar and line chart
 * - Multiple growth metrics
 * - Responsive design
 * - Interactive tooltips
 * - Growth rate indicators
 */
const GrowthChart = ({ 
  data = [], 
  height = 300, 
  showLegend = true,
  className = "",
  title = "Growth Trends"
}) => {
  // Sample growth data
  const defaultData = [
    { 
      month: 'Jan', 
      users: 120, 
      missions: 45, 
      revenue: 2400,
      userGrowth: 8.2,
      missionGrowth: 12.5,
      revenueGrowth: 15.3
    },
    { 
      month: 'Feb', 
      users: 145, 
      missions: 52, 
      revenue: 3200,
      userGrowth: 20.8,
      missionGrowth: 15.6,
      revenueGrowth: 33.3
    },
    { 
      month: 'Mar', 
      users: 168, 
      missions: 48, 
      revenue: 2800,
      userGrowth: 15.9,
      missionGrowth: -7.7,
      revenueGrowth: -12.5
    },
    { 
      month: 'Apr', 
      users: 195, 
      missions: 67, 
      revenue: 4100,
      userGrowth: 16.1,
      missionGrowth: 39.6,
      revenueGrowth: 46.4
    },
    { 
      month: 'May', 
      users: 220, 
      missions: 73, 
      revenue: 3600,
      userGrowth: 12.8,
      missionGrowth: 9.0,
      revenueGrowth: -12.2
    },
    { 
      month: 'Jun', 
      users: 258, 
      missions: 89, 
      revenue: 4800,
      userGrowth: 17.3,
      missionGrowth: 21.9,
      revenueGrowth: 33.3
    }
  ];

  const chartData = data.length > 0 ? data : defaultData;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100 mb-2">{label}</p>
          {payload.map((entry, index) => {
            const isGrowthRate = entry.dataKey.includes('Growth');
            const value = isGrowthRate ? `${entry.value}%` : entry.value.toLocaleString();
            return (
              <p key={index} style={{ color: entry.color }} className="text-sm">
                {entry.name}: {value}
              </p>
            );
          })}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">📊</span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <Chip color="success" variant="flat" size="sm">
            Growth
          </Chip>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <div style={{ width: '100%', height: height }}>
          <ResponsiveContainer>
            <ComposedChart
              data={chartData}
              margin={{ top: 20, right: 30, bottom: 20, left: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="month" 
                stroke="#64748b"
                fontSize={12}
              />
              <YAxis 
                yAxisId="left"
                stroke="#64748b"
                fontSize={12}
              />
              <YAxis 
                yAxisId="right" 
                orientation="right"
                stroke="#64748b"
                fontSize={12}
                tickFormatter={(value) => `${value}%`}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && <Legend />}
              
              {/* Bar charts for absolute values */}
              <Bar 
                yAxisId="left"
                dataKey="users" 
                fill="#3b82f6" 
                name="Users"
                radius={[2, 2, 0, 0]}
              />
              <Bar 
                yAxisId="left"
                dataKey="missions" 
                fill="#10b981" 
                name="Missions"
                radius={[2, 2, 0, 0]}
              />
              
              {/* Line charts for growth rates */}
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="userGrowth"
                stroke="#8b5cf6"
                strokeWidth={3}
                dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#8b5cf6', strokeWidth: 2 }}
                name="User Growth %"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="revenueGrowth"
                stroke="#f59e0b"
                strokeWidth={3}
                dot={{ fill: '#f59e0b', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#f59e0b', strokeWidth: 2 }}
                name="Revenue Growth %"
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </CardBody>
    </Card>
  );
};

export default GrowthChart;
