#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix context imports in a file
function fixContextImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Calculate the correct import path based on file location
    const relativePath = path.relative(path.dirname(filePath), 'client/src/contexts');
    const correctPath = path.join(relativePath, 'supabase-auth.context.jsx').replace(/\\/g, '/');

    // Fix the specific incorrect import patterns
    const patterns = [
      /import\s+{\s*UserContext\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context['"];?/g,
      /import\s+{\s*UserContext\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context\.jsx['"];?/g,
      /import\s+{\s*UserContext\s*}\s+from\s+["']\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context["'];?/g,
      /import\s+{\s*UserContext\s*}\s+from\s+["']\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context\.jsx["'];?/g,
      /import\s+{\s*UserContext\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context['"];?/g,
      /import\s+{\s*UserContext\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/\.\.\/contexts\/supabase-auth\.context\.jsx['"];?/g,
    ];

    patterns.forEach(pattern => {
      if (pattern.test(content)) {
        content = content.replace(pattern, `import { UserContext } from '${correctPath}';`);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed context import in: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to recursively find and fix files
function fixImportsInDirectory(dir) {
  const items = fs.readdirSync(dir);
  let fixedCount = 0;

  items.forEach(item => {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      fixedCount += fixImportsInDirectory(fullPath);
    } else if (stat.isFile() && (item.endsWith('.jsx') || item.endsWith('.js'))) {
      if (fixContextImportsInFile(fullPath)) {
        fixedCount++;
      }
    }
  });

  return fixedCount;
}

// Main execution
console.log('🔧 Fixing context import paths...');

const clientSrcDir = path.join(__dirname, 'client/src');
if (fs.existsSync(clientSrcDir)) {
  const fixedCount = fixImportsInDirectory(clientSrcDir);
  console.log(`✅ Fixed context imports in ${fixedCount} files!`);
} else {
  console.error('❌ Client src directory not found');
  process.exit(1);
}
