-- Fix infinite recursion in team_members RLS policies
-- Run this in Supabase Dashboard > SQL Editor

-- Step 1: Temporarily disable <PERSON><PERSON> to fix the policies
ALTER TABLE public.team_members DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop the problematic recursive policies
DROP POLICY IF EXISTS "Team members are viewable by team members" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be added by team admins" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be updated by team admins" ON public.team_members;
DROP POLICY IF EXISTS "Team members can be deleted by team admins" ON public.team_members;

-- Step 3: Create a helper function to safely check admin status (avoids recursion)
CREATE OR REPLACE FUNCTION public.is_team_admin(team_id_param UUID, user_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    is_admin_result BOOLEAN := FALSE;
BEGIN
    -- Direct query without triggering RLS
    SELECT is_admin INTO is_admin_result
    FROM public.team_members
    WHERE team_id = team_id_param
    AND user_id = user_id_param
    LIMIT 1;
    
    RETURN COALESCE(is_admin_result, FALSE);
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;

-- Step 4: Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.is_team_admin TO authenticated;

-- Step 5: Create non-recursive policies

-- Policy 1: Users can view their own team memberships
CREATE POLICY "Users can view own memberships" ON public.team_members
    FOR SELECT
    USING (user_id = auth.uid());

-- Policy 2: Team creators can manage all team members
CREATE POLICY "Team creators can manage members" ON public.team_members
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = team_members.team_id
            AND teams.created_by = auth.uid()
        )
    );

-- Policy 3: Team admins can view all team members (using the safe function)
CREATE POLICY "Team admins can view members" ON public.team_members
    FOR SELECT
    USING (
        public.is_team_admin(team_id, auth.uid()) = TRUE
    );

-- Policy 4: Team admins can insert new members (using the safe function)
CREATE POLICY "Team admins can add members" ON public.team_members
    FOR INSERT
    WITH CHECK (
        public.is_team_admin(team_id, auth.uid()) = TRUE
    );

-- Policy 5: Team admins can update members (using the safe function)
CREATE POLICY "Team admins can update members" ON public.team_members
    FOR UPDATE
    USING (
        public.is_team_admin(team_id, auth.uid()) = TRUE
    );

-- Policy 6: Team admins can delete members (using the safe function)
CREATE POLICY "Team admins can delete members" ON public.team_members
    FOR DELETE
    USING (
        public.is_team_admin(team_id, auth.uid()) = TRUE
    );

-- Step 6: Re-enable RLS
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- Step 7: Test the fix
SELECT 'RLS policies fixed successfully for team_members table' as status;
