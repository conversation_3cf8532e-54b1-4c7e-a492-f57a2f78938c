// Component Integration Tests
const { test, expect } = require('@playwright/test');

test.describe('Component Integration', () => {
  test('should load Mission Board component correctly', async ({ page }) => {
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Check for mission board tabs
    await expect(page.locator('text=All Missions')).toBeVisible();
    await expect(page.locator('text=My Missions')).toBeVisible();
    await expect(page.locator('text=Available')).toBeVisible();
    
    // Check for mission cards or empty state
    const missionCards = page.locator('[data-testid="mission-card"]');
    const emptyState = page.locator('text=No missions available');
    
    // Either missions exist or empty state is shown
    await expect(missionCards.first().or(emptyState)).toBeVisible();
  });

  test('should load Alliance Dashboard component correctly', async ({ page }) => {
    await page.goto('/alliances');
    await page.waitForLoadState('networkidle');
    
    // Check for alliance list or creation prompt
    const allianceList = page.locator('[data-testid="alliance-list"]');
    const createButton = page.locator('text=Create Alliance');
    const emptyState = page.locator('text=No alliances');
    
    // Should show either alliances, create button, or empty state
    await expect(allianceList.or(createButton).or(emptyState)).toBeVisible();
  });

  test('should load Skill Verification Dashboard correctly', async ({ page }) => {
    await page.goto('/vetting');
    await page.waitForLoadState('networkidle');
    
    // Check for skill verification elements
    await expect(page.locator('text=Skill').or(page.locator('text=Verification'))).toBeVisible();
    
    // Check for skill levels or assessment options
    const skillLevels = page.locator('[data-testid="skill-level"]');
    const assessmentButton = page.locator('text=Start Assessment');
    
    await expect(skillLevels.first().or(assessmentButton)).toBeVisible();
  });

  test('should load Venture Page component correctly', async ({ page }) => {
    await page.goto('/ventures');
    await page.waitForLoadState('networkidle');
    
    // Check for venture management elements
    await expect(page.locator('text=Venture').or(page.locator('text=Project'))).toBeVisible();
  });

  test('should load Quest Board component correctly', async ({ page }) => {
    await page.goto('/quests');
    await page.waitForLoadState('networkidle');
    
    // Check for quest board elements
    await expect(page.locator('text=Quest')).toBeVisible();
    
    // Check for quest cards or empty state
    const questCards = page.locator('[data-testid="quest-card"]');
    const emptyState = page.locator('text=No quests');
    
    await expect(questCards.first().or(emptyState)).toBeVisible();
  });

  test('should handle component loading states', async ({ page }) => {
    // Intercept network requests to simulate slow loading
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 100);
    });
    
    await page.goto('/missions');
    
    // Should show loading state initially
    const loadingIndicator = page.locator('text=Loading').or(page.locator('[data-testid="loading"]'));
    
    // Wait for content to load
    await page.waitForLoadState('networkidle');
    
    // Loading should be gone and content should be visible
    await expect(page.locator('text=Mission')).toBeVisible();
  });

  test('should handle component error states gracefully', async ({ page }) => {
    // Test with network failures
    await page.route('**/*.js', route => route.abort());
    
    await page.goto('/missions');
    
    // Should handle errors gracefully (not crash the page)
    // The page should still be responsive
    await expect(page.locator('body')).toBeVisible();
  });

  test('should have responsive design on mobile', async ({ page, isMobile }) => {
    if (!isMobile) {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
    }
    
    await page.goto('/missions');
    await page.waitForLoadState('networkidle');
    
    // Check that content is visible and properly sized on mobile
    await expect(page.locator('body')).toBeVisible();
    
    // Check for mobile-specific navigation if it exists
    const mobileNav = page.locator('[data-testid="mobile-navigation"]');
    if (await mobileNav.count() > 0) {
      await expect(mobileNav).toBeVisible();
    }
  });

  test('should have accessible navigation', async ({ page }) => {
    await page.goto('/');
    
    // Check for proper ARIA labels and roles
    const navigation = page.locator('nav, [role="navigation"]');
    if (await navigation.count() > 0) {
      await expect(navigation.first()).toBeVisible();
    }
    
    // Check for skip links
    const skipLinks = page.locator('a[href="#main-content"], [data-testid="skip-link"]');
    if (await skipLinks.count() > 0) {
      await expect(skipLinks.first()).toBeVisible();
    }
  });
});
