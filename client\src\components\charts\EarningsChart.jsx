import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  ReferenceLine
} from 'recharts';
import { Card, CardBody, CardHeader, Chip } from '@heroui/react';

/**
 * Earnings Chart Component - Detailed Earnings Visualization
 * 
 * Features:
 * - Stacked bar chart for earnings breakdown
 * - Reference lines for goals and targets
 * - Interactive tooltips with detailed information
 * - Responsive design
 * - Multiple earning categories
 */
const EarningsChart = ({ 
  data = [], 
  height = 300, 
  showLegend = true,
  showGoalLine = true,
  goalAmount = 4000,
  className = "",
  title = "Earnings Breakdown"
}) => {
  // Sample earnings data
  const defaultData = [
    { 
      month: 'Jan',
      missions: 1200,
      bounties: 800,
      ventures: 400,
      orbs: 150,
      total: 2550,
      goal: 4000
    },
    { 
      month: 'Feb',
      missions: 1500,
      bounties: 1200,
      ventures: 500,
      orbs: 180,
      total: 3380,
      goal: 4000
    },
    { 
      month: 'Mar',
      missions: 1100,
      bounties: 900,
      ventures: 800,
      orbs: 200,
      total: 3000,
      goal: 4000
    },
    { 
      month: 'Apr',
      missions: 1800,
      bounties: 1500,
      ventures: 600,
      orbs: 220,
      total: 4120,
      goal: 4000
    },
    { 
      month: 'May',
      missions: 1600,
      bounties: 1100,
      ventures: 900,
      orbs: 250,
      total: 3850,
      goal: 4000
    },
    { 
      month: 'Jun',
      missions: 2000,
      bounties: 1800,
      ventures: 1000,
      orbs: 300,
      total: 5100,
      goal: 4000
    }
  ];

  const chartData = data.length > 0 ? data : defaultData;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const total = payload.reduce((sum, entry) => sum + entry.value, 0);
      return (
        <div className="bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100 mb-2">{label}</p>
          <p className="text-sm font-medium mb-2">Total: ${total.toLocaleString()}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: ${entry.value.toLocaleString()}
            </p>
          ))}
          {showGoalLine && (
            <p className="text-sm text-default-600 mt-2 pt-2 border-t border-default-200">
              Goal: ${goalAmount.toLocaleString()}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">💰</span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <Chip color="success" variant="flat" size="sm">
            Earnings
          </Chip>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <div style={{ width: '100%', height: height }}>
          <ResponsiveContainer>
            <BarChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="month" 
                stroke="#64748b"
                fontSize={12}
              />
              <YAxis 
                stroke="#64748b"
                fontSize={12}
                tickFormatter={(value) => `$${value.toLocaleString()}`}
              />
              <Tooltip content={<CustomTooltip />} />
              {showLegend && <Legend />}
              
              {/* Reference line for goal */}
              {showGoalLine && (
                <ReferenceLine 
                  y={goalAmount} 
                  stroke="#ef4444" 
                  strokeDasharray="5 5"
                  label={{ value: "Goal", position: "topRight" }}
                />
              )}
              
              {/* Stacked bars for different earning types */}
              <Bar 
                dataKey="missions" 
                stackId="earnings"
                fill="#10b981" 
                name="Missions"
                radius={[0, 0, 0, 0]}
              />
              <Bar 
                dataKey="bounties" 
                stackId="earnings"
                fill="#3b82f6" 
                name="Bounties"
                radius={[0, 0, 0, 0]}
              />
              <Bar 
                dataKey="ventures" 
                stackId="earnings"
                fill="#8b5cf6" 
                name="Ventures"
                radius={[0, 0, 0, 0]}
              />
              <Bar 
                dataKey="orbs" 
                stackId="earnings"
                fill="#f59e0b" 
                name="ORB Points"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardBody>
    </Card>
  );
};

export default EarningsChart;
