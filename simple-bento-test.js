#!/usr/bin/env node

/**
 * Simple Bento Grid Test
 * 
 * A straightforward test to check the current state of the bento grid
 * without complex authentication setup.
 */

const { chromium } = require('playwright');

async function testBentoGrid() {
  console.log('🧪 Simple Bento Grid Navigation Test');
  console.log('='.repeat(40));

  const browser = await chromium.launch({ 
    headless: false, // Show browser
    slowMo: 1000 // Slow down for visibility
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🌐 Navigating to localhost:5173...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Take a screenshot of the initial state
    await page.screenshot({ path: 'test-results/initial-page.png', fullPage: true });
    console.log('📸 Screenshot saved: test-results/initial-page.png');

    // Check what we see on the page
    console.log('\n🔍 Analyzing page content...');
    
    // Check for login form
    const hasLoginForm = await page.locator('input[type="email"]').count() > 0;
    console.log(`Login form present: ${hasLoginForm}`);

    // Check for bento grid elements
    const bentoSelectors = [
      '[data-canvas-card]',
      '.grid',
      '[data-testid="experimental-navigation"]',
      '.card',
      '[role="gridcell"]'
    ];

    let bentoGridFound = false;
    let foundSelector = '';
    
    for (const selector of bentoSelectors) {
      const count = await page.locator(selector).count();
      if (count > 0) {
        console.log(`✅ Found ${count} elements with selector: ${selector}`);
        bentoGridFound = true;
        foundSelector = selector;
        break;
      } else {
        console.log(`❌ No elements found with selector: ${selector}`);
      }
    }

    if (hasLoginForm) {
      console.log('\n🔐 Login form detected. Let\'s see what happens if we try to bypass...');
      
      // Try to bypass authentication by setting localStorage
      await page.evaluate(() => {
        // Mock Supabase session
        const mockSession = {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          expires_in: 3600,
          token_type: 'bearer',
          user: {
            id: 'test-user-id',
            email: '<EMAIL>',
            user_metadata: {
              full_name: 'Test User'
            }
          }
        };
        
        // Set in localStorage
        localStorage.setItem('supabase.auth.token', JSON.stringify(mockSession));
        localStorage.setItem('sb-hqqlrrqvjcetoxbdjgzx-auth-token', JSON.stringify(mockSession));
        
        // Set user context
        window.__TEST_USER__ = mockSession.user;
        window.__AUTHENTICATED__ = true;
        
        console.log('Mock auth set in localStorage');
      });

      // Reload the page
      console.log('🔄 Reloading page with mock auth...');
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Take another screenshot
      await page.screenshot({ path: 'test-results/after-mock-auth.png', fullPage: true });
      console.log('📸 Screenshot saved: test-results/after-mock-auth.png');
      
      // Check again for bento grid
      for (const selector of bentoSelectors) {
        const count = await page.locator(selector).count();
        if (count > 0) {
          console.log(`✅ After auth bypass - Found ${count} elements with selector: ${selector}`);
          bentoGridFound = true;
          foundSelector = selector;
          break;
        }
      }
    }

    if (bentoGridFound) {
      console.log(`\n🎯 Bento grid found! Using selector: ${foundSelector}`);
      
      // Get all the tiles
      const tiles = page.locator(foundSelector);
      const tileCount = await tiles.count();
      console.log(`📊 Found ${tileCount} bento grid tiles`);
      
      // Test clicking the first few tiles
      for (let i = 0; i < Math.min(tileCount, 3); i++) {
        const tile = tiles.nth(i);
        
        // Get tile text for identification
        const tileText = await tile.textContent();
        console.log(`\n🖱️  Testing tile ${i + 1}: "${tileText?.substring(0, 50)}..."`);
        
        try {
          // Click the tile
          await tile.click();
          await page.waitForLoadState('networkidle');
          
          // Check the URL
          const currentUrl = page.url();
          console.log(`   ✅ Navigated to: ${currentUrl}`);
          
          // Take a screenshot
          await page.screenshot({ 
            path: `test-results/tile-${i + 1}-result.png`, 
            fullPage: true 
          });
          
          // Go back to home
          await page.goto('http://localhost:5173');
          await page.waitForLoadState('networkidle');
          
        } catch (error) {
          console.log(`   ❌ Error clicking tile: ${error.message}`);
        }
      }
      
    } else {
      console.log('\n❌ No bento grid elements found');
      console.log('🔍 Let\'s see what IS on the page...');
      
      // Get page title
      const title = await page.title();
      console.log(`Page title: ${title}`);
      
      // Get some text content
      const bodyText = await page.locator('body').textContent();
      console.log(`Page content preview: ${bodyText?.substring(0, 200)}...`);
      
      // Look for any clickable elements
      const buttons = await page.locator('button').count();
      const links = await page.locator('a').count();
      const cards = await page.locator('[class*="card"]').count();
      
      console.log(`Found: ${buttons} buttons, ${links} links, ${cards} card-like elements`);
    }

    console.log('\n📋 Test Summary:');
    console.log(`- Login form: ${hasLoginForm ? 'Present' : 'Not found'}`);
    console.log(`- Bento grid: ${bentoGridFound ? 'Found' : 'Not found'}`);
    console.log(`- Screenshots saved in test-results/`);
    
    // Keep browser open for manual inspection
    console.log('\n👀 Browser will stay open for 30 seconds for manual inspection...');
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'test-results/error-state.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('\n✅ Test completed!');
  }
}

// Run the test
testBentoGrid().catch(console.error);
