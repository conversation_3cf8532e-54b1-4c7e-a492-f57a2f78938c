#!/bin/bash

# Royaltea Platform - AI Agent Deployment Script
# Automates the setup and deployment of specialized AI agents

set -e

echo "🤖 Deploying AI Agents for Royaltea Platform"
echo "============================================="

# Configuration
GITHUB_REPO="CityOfGamers/royaltea"
ISSUE_NUMBER="10"
PROJECT_DIR="$(pwd)/.."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install Git first."
    exit 1
fi

if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Prerequisites check passed"

# Verify we're in the right directory
if [ ! -f "$PROJECT_DIR/client/package.json" ]; then
    print_error "Cannot find client/package.json. Please run this script from the agent-setup directory."
    exit 1
fi

print_status "Project structure verified"

# Create agent workspace
echo "📁 Setting up agent workspace..."
mkdir -p agents/{environment,page-integration,component-enhancement,testing,ui-polish}
mkdir -p agents/shared/{solutions,test-results,code-patterns}

# Create agent assignment tracking
cat > agents/shared/agent-assignments.json << 'EOF'
{
  "assignments": {},
  "completed": {},
  "in_progress": {},
  "available": [
    "J1", "J2", "J3", "J4", "J5", "J6", "J7", "J8", "J9", 
    "J10", "J11", "J12", "J13", "J14", "J15"
  ]
}
EOF

print_status "Agent workspace created"

# Create deployment configurations
echo "⚙️  Creating deployment configurations..."

# Environment agent configuration
cat > agents/environment/config.json << 'EOF'
{
  "agent_type": "environment",
  "task": "J1",
  "priority": "critical",
  "estimated_hours": 3,
  "dependencies": [],
  "blocks": ["J2", "J3", "J4", "J5", "J6", "J7", "J8", "J9", "J10", "J11", "J12"],
  "deliverables": [
    "client/.env.local",
    "API connection tests",
    "Environment documentation"
  ]
}
EOF

# Page integration agent configurations
for task in J2 J3 J4 J5 J6 J9; do
    cat > "agents/page-integration/${task}-config.json" << EOF
{
  "agent_type": "page-integration",
  "task": "${task}",
  "priority": "critical",
  "estimated_hours": 4,
  "dependencies": ["J1"],
  "blocks": [],
  "deliverables": [
    "Page component",
    "Route configuration",
    "Navigation integration",
    "User journey test"
  ]
}
EOF
done

print_status "Deployment configurations created"

# Create monitoring script
cat > agents/shared/monitor-progress.sh << 'EOF'
#!/bin/bash

# Agent Progress Monitoring Script

echo "📊 Agent Progress Monitor"
echo "========================"

# Check agent assignments
if [ -f "agent-assignments.json" ]; then
    echo "📋 Current Assignments:"
    cat agent-assignments.json | jq -r '.assignments | to_entries[] | "  \(.key): \(.value)"'
    echo ""
    
    echo "✅ Completed Tasks:"
    cat agent-assignments.json | jq -r '.completed | keys[]' | sed 's/^/  /'
    echo ""
    
    echo "🔄 In Progress:"
    cat agent-assignments.json | jq -r '.in_progress | keys[]' | sed 's/^/  /'
    echo ""
    
    echo "🔴 Available Tasks:"
    cat agent-assignments.json | jq -r '.available[]' | sed 's/^/  /'
else
    echo "⚠️  No assignment tracking file found"
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Assign available tasks to agents"
echo "2. Monitor progress every 24 hours"
echo "3. Update assignments as tasks complete"
EOF

chmod +x agents/shared/monitor-progress.sh

print_status "Monitoring system created"

# Create quick deployment commands
cat > agents/shared/quick-commands.sh << 'EOF'
#!/bin/bash

# Quick Commands for Agent Management

# Assign task to agent
assign_task() {
    local agent_id=$1
    local task_id=$2
    echo "Assigning $task_id to $agent_id..."
    # Update assignment tracking
    jq --arg agent "$agent_id" --arg task "$task_id" \
       '.assignments[$task] = $agent | .available = (.available - [$task]) | .in_progress[$task] = $agent' \
       agent-assignments.json > tmp.json && mv tmp.json agent-assignments.json
}

# Mark task complete
complete_task() {
    local task_id=$1
    echo "Marking $task_id as complete..."
    jq --arg task "$task_id" \
       '.completed[$task] = .in_progress[$task] | del(.in_progress[$task])' \
       agent-assignments.json > tmp.json && mv tmp.json agent-assignments.json
}

# Show available tasks
show_available() {
    echo "Available tasks:"
    jq -r '.available[]' agent-assignments.json
}

# Show progress summary
show_progress() {
    echo "Progress Summary:"
    echo "Completed: $(jq '.completed | length' agent-assignments.json)"
    echo "In Progress: $(jq '.in_progress | length' agent-assignments.json)"
    echo "Available: $(jq '.available | length' agent-assignments.json)"
}

# Export functions
export -f assign_task complete_task show_available show_progress
EOF

chmod +x agents/shared/quick-commands.sh

print_status "Quick commands created"

# Verify client setup
echo "🔧 Verifying client setup..."
cd "$PROJECT_DIR/client"

if [ ! -d "node_modules" ]; then
    print_warning "Installing client dependencies..."
    npm install
    print_status "Dependencies installed"
else
    print_status "Dependencies already installed"
fi

# Test development server
print_info "Testing development server..."
timeout 10s npm run dev > /dev/null 2>&1 || true
print_status "Development server test completed"

cd - > /dev/null

# Create agent deployment summary
echo "📋 Creating deployment summary..."

cat > agents/deployment-summary.md << 'EOF'
# Agent Deployment Summary

## 🚀 Deployment Status: READY

### Agent Types Deployed
- ✅ Environment Agent (1) - J1
- ✅ Page Integration Agents (6) - J2-J6, J9
- ✅ Component Enhancement Agents (2) - J7-J8
- ✅ Testing Agents (3) - J10-J12
- ✅ UI Polish Agents (3) - J13-J15

### Total Capacity: 15 parallel agents

## 📊 Deployment Metrics
- **Setup Time**: ~5 minutes
- **Total Tasks**: 15 modular tasks
- **Estimated Completion**: 1-3 days
- **Parallel Execution**: ✅ Enabled

## 🎯 Next Steps
1. Assign agents to tasks via GitHub Issue #10
2. Monitor progress with `./agents/shared/monitor-progress.sh`
3. Track completion with agent assignment system

## 📞 Support
- Task details: `docs/design-system/agent-task-queue.md`
- Quick start: `agent-setup/quick-start.md`
- GitHub coordination: Issue #10
EOF

print_status "Deployment summary created"

# Final status
echo ""
echo "🎉 Agent Deployment Complete!"
echo "=============================="
echo ""
print_info "Agent workspace: $(pwd)/agents/"
print_info "Monitoring: ./agents/shared/monitor-progress.sh"
print_info "Quick commands: ./agents/shared/quick-commands.sh"
print_info "GitHub coordination: https://github.com/$GITHUB_REPO/issues/$ISSUE_NUMBER"
echo ""
print_status "Ready to deploy 15 AI agents for parallel task execution"
echo ""
echo "🚀 Next Steps:"
echo "1. Review agent prompts in agents/*/prompt.md"
echo "2. Assign agents to tasks on GitHub Issue #10"
echo "3. Monitor progress with monitoring tools"
echo "4. Expect completion in 1-3 days"
echo ""
print_info "Platform transformation: 'components exist' → 'users can access everything'"
