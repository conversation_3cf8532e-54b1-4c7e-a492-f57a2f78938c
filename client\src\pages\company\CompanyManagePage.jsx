import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, CardBody, Button, Spinner } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-toastify';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import CompanyDetails from '../../components/alliance/CompanyDetails';

/**
 * CompanyManagePage - Dedicated page for managing business entities
 * Provides comprehensive company management interface
 */
const CompanyManagePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [canEdit, setCanEdit] = useState(false);

  useEffect(() => {
    if (id && currentUser) {
      fetchCompanyData();
    }
  }, [id, currentUser]);

  const fetchCompanyData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch company data using the companies API
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        throw new Error('Authentication required');
      }

      const response = await fetch(`/.netlify/functions/companies/${id}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Company not found');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to manage this company');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch company data');
        }
      }

      const result = await response.json();
      setCompany(result.company);

      // Check if user can edit this company
      // User can edit if they are admin of any team linked to this company
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select(`
          id,
          team_members!inner(user_id, is_admin)
        `)
        .eq('company_id', id)
        .eq('team_members.user_id', currentUser.id)
        .eq('team_members.is_admin', true);

      if (teamsError) {
        console.error('Error checking edit permissions:', teamsError);
      } else {
        setCanEdit(teams && teams.length > 0);
      }

    } catch (error) {
      console.error('Error fetching company data:', error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCompanyUpdate = () => {
    // Refresh company data after update
    fetchCompanyData();
    toast.success('Company updated successfully');
  };

  const handleBackToTeams = () => {
    navigate('/teams');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="bg-white/10 backdrop-blur-md">
          <CardBody className="p-8 text-center">
            <Spinner size="lg" color="primary" />
            <p className="text-white mt-4">Loading company data...</p>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="bg-white/10 backdrop-blur-md max-w-md">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">❌</span>
            <h2 className="text-xl font-bold text-white mb-2">Error Loading Company</h2>
            <p className="text-white/60 mb-4">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button
                onClick={handleBackToTeams}
                className="bg-white/20 text-white hover:bg-white/30"
              >
                Back to Teams
              </Button>
              <Button
                onClick={fetchCompanyData}
                color="primary"
              >
                Try Again
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="bg-white/10 backdrop-blur-md max-w-md">
          <CardBody className="p-8 text-center">
            <span className="text-6xl mb-4 block">🏢</span>
            <h2 className="text-xl font-bold text-white mb-2">Company Not Found</h2>
            <p className="text-white/60 mb-4">The requested company could not be found.</p>
            <Button
              onClick={handleBackToTeams}
              className="bg-white/20 text-white hover:bg-white/30"
            >
              Back to Teams
            </Button>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button
            onClick={handleBackToTeams}
            className="mb-4 bg-white/10 text-white hover:bg-white/20"
            startContent={<span>←</span>}
          >
            Back to Teams
          </Button>
          
          <Card className="bg-white/10 backdrop-blur-md border-white/20">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <span className="text-3xl">🏢</span>
                  <div>
                    <h1 className="text-2xl font-bold text-white">{company.legal_name}</h1>
                    <p className="text-white/60">
                      {company.company_type?.toUpperCase()} • {company.tax_id}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    company.compliance_status === 'active' 
                      ? 'bg-green-500/20 text-green-300' 
                      : 'bg-yellow-500/20 text-yellow-300'
                  }`}>
                    {company.compliance_status?.toUpperCase()}
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Company Details */}
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <span className="text-xl">📋</span>
              <h2 className="text-lg font-semibold text-white">Business Entity Details</h2>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <CompanyDetails
              company={company}
              onUpdate={handleCompanyUpdate}
              canEdit={canEdit}
            />
          </CardBody>
        </Card>

        {/* Additional Management Sections can be added here */}
        {!canEdit && (
          <Card className="bg-yellow-500/10 backdrop-blur-md border-yellow-500/20 mt-6">
            <CardBody className="p-4">
              <div className="flex items-center gap-2 text-yellow-300">
                <span>⚠️</span>
                <p className="text-sm">
                  You have read-only access to this company. Contact an alliance administrator to make changes.
                </p>
              </div>
            </CardBody>
          </Card>
        )}
      </div>
    </motion.div>
  );
};

export default CompanyManagePage;
