#!/usr/bin/env node

/**
 * Test Setup Verification Script
 * 
 * Verifies that the testing environment is properly configured
 * for authenticated bento grid navigation testing.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Bento Grid Test Setup');
console.log('='.repeat(40));

// Check Node.js version
console.log('📦 Checking Node.js version...');
try {
  const nodeVersion = process.version;
  console.log(`✅ Node.js version: ${nodeVersion}`);
} catch (error) {
  console.log('❌ Node.js not found');
  process.exit(1);
}

// Check if Playwright is installed
console.log('\n🎭 Checking Playwright installation...');
try {
  const playwrightVersion = execSync('npx playwright --version', { encoding: 'utf8' }).trim();
  console.log(`✅ ${playwrightVersion}`);
} catch (error) {
  console.log('❌ Playwright not installed');
  console.log('💡 Run: npm install @playwright/test');
  process.exit(1);
}

// Check if browsers are installed
console.log('\n🌐 Checking browser installations...');
try {
  execSync('npx playwright install --dry-run', { stdio: 'pipe' });
  console.log('✅ Playwright browsers are installed');
} catch (error) {
  console.log('⚠️  Some browsers may need installation');
  console.log('💡 Run: npx playwright install');
}

// Check test files exist
console.log('\n📁 Checking test files...');
const requiredFiles = [
  'tests/bento-grid-navigation.spec.js',
  'playwright-auth.config.js',
  'tests/auth-global-setup.js',
  'tests/auth-global-teardown.js',
  'test-bento-grid.js'
];

for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing`);
  }
}

// Check if development server is running
console.log('\n🚀 Checking development server...');
async function checkDevServer() {
  try {
    const response = await fetch('http://localhost:5173');
    return response.ok;
  } catch (error) {
    return false;
  }
}

checkDevServer().then(isRunning => {
  if (isRunning) {
    console.log('✅ Development server is running on http://localhost:5173');
  } else {
    console.log('⚠️  Development server not detected');
    console.log('💡 Start with: cd client && npm run dev');
  }

  // Check package.json for required dependencies
  console.log('\n📦 Checking dependencies...');
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const clientPackageJson = JSON.parse(fs.readFileSync('client/package.json', 'utf8'));
    
    const requiredDeps = ['@playwright/test'];
    const requiredClientDeps = ['react', 'react-dom', '@heroui/react'];
    
    for (const dep of requiredDeps) {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        console.log(`✅ ${dep}`);
      } else {
        console.log(`❌ ${dep} - Missing from package.json`);
      }
    }
    
    for (const dep of requiredClientDeps) {
      if (clientPackageJson.dependencies?.[dep] || clientPackageJson.devDependencies?.[dep]) {
        console.log(`✅ client/${dep}`);
      } else {
        console.log(`⚠️  client/${dep} - Check client dependencies`);
      }
    }
    
  } catch (error) {
    console.log('⚠️  Could not read package.json files');
  }

  // Create test-results directory if it doesn't exist
  console.log('\n📂 Setting up test directories...');
  const testResultsDir = 'test-results';
  if (!fs.existsSync(testResultsDir)) {
    fs.mkdirSync(testResultsDir, { recursive: true });
    console.log(`✅ Created ${testResultsDir} directory`);
  } else {
    console.log(`✅ ${testResultsDir} directory exists`);
  }

  // Summary and next steps
  console.log('\n📋 Setup Summary');
  console.log('-'.repeat(20));
  console.log('✅ Test files are ready');
  console.log('✅ Playwright is configured');
  console.log('✅ Authentication setup is complete');
  
  console.log('\n🚀 Ready to run tests!');
  console.log('\nQuick start commands:');
  console.log('1. Start dev server: cd client && npm run dev');
  console.log('2. Run tests: node test-bento-grid.js');
  console.log('3. Run with browser visible: node test-bento-grid.js --headed');
  console.log('4. Debug tests: node test-bento-grid.js --debug');
  
  console.log('\n📚 Available test modes:');
  console.log('- Local with auth: node test-bento-grid.js');
  console.log('- Local without auth: node test-bento-grid.js --no-auth');
  console.log('- Production testing: node test-bento-grid.js --production');
  console.log('- Admin features: node test-bento-grid.js --project=admin-tests');
  
}).catch(error => {
  console.log('❌ Error checking development server:', error.message);
});
