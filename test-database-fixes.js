// Test script to verify database query fixes
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: 'client/.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test user ID (replace with actual user ID from your database)
const TEST_USER_ID = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b';

async function testDatabaseQueries() {
  console.log('🧪 Testing Database Query Fixes...\n');

  // Test 1: Projects query with created_by (was failing with owner_id)
  console.log('1️⃣ Testing projects query with created_by...');
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('id, name, created_by, updated_at')
      .eq('created_by', TEST_USER_ID)
      .limit(5);

    if (error) {
      console.log('❌ Projects query failed:', error.message);
    } else {
      console.log('✅ Projects query successful:', data?.length || 0, 'projects found');
    }
  } catch (err) {
    console.log('❌ Projects query error:', err.message);
  }

  // Test 2: Revenue entries query through project_contributors
  console.log('\n2️⃣ Testing revenue entries query...');
  try {
    const { data, error } = await supabase
      .from('revenue_entries')
      .select(`
        id,
        amount,
        project_id,
        projects!inner(
          project_contributors!inner(user_id)
        )
      `)
      .eq('projects.project_contributors.user_id', TEST_USER_ID)
      .limit(5);

    if (error) {
      console.log('❌ Revenue entries query failed:', error.message);
    } else {
      console.log('✅ Revenue entries query successful:', data?.length || 0, 'entries found');
    }
  } catch (err) {
    console.log('❌ Revenue entries query error:', err.message);
  }

  // Test 3: Team members query (simplified approach)
  console.log('\n3️⃣ Testing team members query (step 1 - memberships)...');
  try {
    const { data: memberships, error: membershipError } = await supabase
      .from('team_members')
      .select('*')
      .eq('user_id', TEST_USER_ID);

    if (membershipError) {
      console.log('❌ Team memberships query failed:', membershipError.message);
    } else {
      console.log('✅ Team memberships query successful:', memberships?.length || 0, 'memberships found');
      
      if (memberships && memberships.length > 0) {
        console.log('\n3️⃣ Testing team members query (step 2 - team details)...');
        const teamIds = memberships.map(m => m.team_id);
        
        const { data: teams, error: teamsError } = await supabase
          .from('teams')
          .select(`
            id,
            name,
            description,
            alliance_type,
            created_at
          `)
          .in('id', teamIds);

        if (teamsError) {
          console.log('❌ Teams details query failed:', teamsError.message);
        } else {
          console.log('✅ Teams details query successful:', teams?.length || 0, 'teams found');
        }
      }
    }
  } catch (err) {
    console.log('❌ Team members query error:', err.message);
  }

  // Test 4: Simple contributions query
  console.log('\n4️⃣ Testing contributions query...');
  try {
    const { data, error } = await supabase
      .from('contributions')
      .select('id, user_id, status, created_at')
      .eq('user_id', TEST_USER_ID)
      .limit(5);

    if (error) {
      console.log('❌ Contributions query failed:', error.message);
    } else {
      console.log('✅ Contributions query successful:', data?.length || 0, 'contributions found');
    }
  } catch (err) {
    console.log('❌ Contributions query error:', err.message);
  }

  console.log('\n🎉 Database query testing completed!');
}

// Run the tests
testDatabaseQueries().catch(console.error);
