import React from 'react';
import {
  <PERSON>Chart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { Card, CardBody, CardHeader, Chip } from '@heroui/react';

/**
 * Revenue Chart Component - Interactive Revenue Visualization
 * 
 * Features:
 * - Line and area chart options
 * - Responsive design for all screen sizes
 * - Interactive tooltips with detailed information
 * - Multiple data series support
 * - Smooth animations and transitions
 */
const RevenueChart = ({ 
  data = [], 
  type = 'area', 
  height = 300, 
  showLegend = true,
  className = "",
  title = "Revenue Trends"
}) => {
  // Sample data if none provided
  const defaultData = [
    { month: 'Jan', revenue: 2400, expenses: 1200, profit: 1200 },
    { month: 'Feb', revenue: 3200, expenses: 1400, profit: 1800 },
    { month: 'Mar', revenue: 2800, expenses: 1300, profit: 1500 },
    { month: 'Apr', revenue: 4100, expenses: 1600, profit: 2500 },
    { month: 'May', revenue: 3600, expenses: 1500, profit: 2100 },
    { month: 'Jun', revenue: 4800, expenses: 1800, profit: 3000 },
    { month: 'Jul', revenue: 5200, expenses: 1900, profit: 3300 },
    { month: 'Aug', revenue: 4600, expenses: 1700, profit: 2900 },
    { month: 'Sep', revenue: 5800, expenses: 2100, profit: 3700 },
    { month: 'Oct', revenue: 6200, expenses: 2200, profit: 4000 },
    { month: 'Nov', revenue: 5900, expenses: 2000, profit: 3900 },
    { month: 'Dec', revenue: 7100, expenses: 2400, profit: 4700 }
  ];

  const chartData = data.length > 0 ? data : defaultData;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-slate-800 p-3 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: ${entry.value.toLocaleString()}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Chart component based on type
  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    if (type === 'area') {
      return (
        <AreaChart {...commonProps}>
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
          <XAxis 
            dataKey="month" 
            stroke="#64748b"
            fontSize={12}
          />
          <YAxis 
            stroke="#64748b"
            fontSize={12}
            tickFormatter={(value) => `$${value.toLocaleString()}`}
          />
          <Tooltip content={<CustomTooltip />} />
          {showLegend && <Legend />}
          <Area
            type="monotone"
            dataKey="revenue"
            stroke="#10b981"
            strokeWidth={2}
            fill="url(#revenueGradient)"
            name="Revenue"
          />
          <Area
            type="monotone"
            dataKey="profit"
            stroke="#3b82f6"
            strokeWidth={2}
            fill="url(#profitGradient)"
            name="Profit"
          />
        </AreaChart>
      );
    }

    return (
      <LineChart {...commonProps}>
        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
        <XAxis 
          dataKey="month" 
          stroke="#64748b"
          fontSize={12}
        />
        <YAxis 
          stroke="#64748b"
          fontSize={12}
          tickFormatter={(value) => `$${value.toLocaleString()}`}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend />}
        <Line
          type="monotone"
          dataKey="revenue"
          stroke="#10b981"
          strokeWidth={3}
          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
          name="Revenue"
        />
        <Line
          type="monotone"
          dataKey="profit"
          stroke="#3b82f6"
          strokeWidth={3}
          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
          name="Profit"
        />
        <Line
          type="monotone"
          dataKey="expenses"
          stroke="#ef4444"
          strokeWidth={2}
          dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5, stroke: '#ef4444', strokeWidth: 2 }}
          name="Expenses"
          strokeDasharray="5 5"
        />
      </LineChart>
    );
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">📈</span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <Chip color="primary" variant="flat" size="sm">
            Interactive
          </Chip>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <div style={{ width: '100%', height: height }}>
          <ResponsiveContainer>
            {renderChart()}
          </ResponsiveContainer>
        </div>
      </CardBody>
    </Card>
  );
};

export default RevenueChart;
