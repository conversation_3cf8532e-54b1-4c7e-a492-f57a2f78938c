// JavaScript Loading and Execution Debug Tests
const { test, expect } = require('@playwright/test');

test.describe('JavaScript Debug Tests', () => {
  test('should check JavaScript bundle loading', async ({ page }) => {
    const jsErrors = [];
    const networkRequests = [];
    const failedRequests = [];

    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    // Capture network requests
    page.on('request', request => {
      if (request.url().includes('.js') || request.url().includes('.css')) {
        networkRequests.push({
          url: request.url(),
          method: request.method(),
          type: request.resourceType()
        });
      }
    });

    // Capture failed requests
    page.on('requestfailed', request => {
      failedRequests.push({
        url: request.url(),
        method: request.method(),
        error: request.failure()?.errorText
      });
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    console.log('=== JAVASCRIPT BUNDLE ANALYSIS ===');
    console.log('JavaScript requests:', networkRequests.filter(r => r.url.includes('.js')).length);
    console.log('CSS requests:', networkRequests.filter(r => r.url.includes('.css')).length);
    
    // Show first few JS files
    const jsFiles = networkRequests.filter(r => r.url.includes('.js')).slice(0, 5);
    console.log('JS files loaded:', jsFiles.map(f => f.url.split('/').pop()));

    console.log('=== JAVASCRIPT ERRORS ===');
    console.log('Total JS errors:', jsErrors.length);
    if (jsErrors.length > 0) {
      console.log('First 3 errors:', jsErrors.slice(0, 3));
    }

    console.log('=== FAILED REQUESTS ===');
    console.log('Failed requests:', failedRequests.length);
    if (failedRequests.length > 0) {
      console.log('Failed:', failedRequests.slice(0, 3));
    }

    // Check if React is available
    const reactCheck = await page.evaluate(() => {
      return {
        hasReact: typeof window.React !== 'undefined',
        hasReactDOM: typeof window.ReactDOM !== 'undefined',
        hasApp: !!document.getElementById('root'),
        rootContent: document.getElementById('root')?.innerHTML?.substring(0, 200) || 'No root element'
      };
    });

    console.log('=== REACT STATUS ===');
    console.log('React available:', reactCheck.hasReact);
    console.log('ReactDOM available:', reactCheck.hasReactDOM);
    console.log('Root element exists:', reactCheck.hasApp);
    console.log('Root content:', reactCheck.rootContent);

    expect(true).toBe(true);
  });

  test('should check module import errors', async ({ page }) => {
    const moduleErrors = [];
    
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('import') || text.includes('module') || text.includes('flat') || text.includes('export')) {
        moduleErrors.push(text);
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    console.log('=== MODULE IMPORT ERRORS ===');
    console.log('Module-related errors:', moduleErrors.length);
    if (moduleErrors.length > 0) {
      console.log('Module errors:', moduleErrors);
    }

    // Check for specific flat module error
    const flatError = moduleErrors.find(err => err.includes('flat'));
    if (flatError) {
      console.log('FLAT MODULE ERROR FOUND:', flatError);
    }

    expect(true).toBe(true);
  });

  test('should check HTML structure', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    const htmlStructure = await page.evaluate(() => {
      return {
        hasHead: !!document.head,
        hasBody: !!document.body,
        hasRoot: !!document.getElementById('root'),
        headContent: document.head?.innerHTML?.substring(0, 500) || 'No head',
        bodyContent: document.body?.innerHTML?.substring(0, 500) || 'No body',
        rootContent: document.getElementById('root')?.innerHTML || 'No root element',
        scripts: Array.from(document.querySelectorAll('script')).map(s => ({
          src: s.src,
          type: s.type,
          hasContent: s.innerHTML.length > 0
        }))
      };
    });

    console.log('=== HTML STRUCTURE ===');
    console.log('Has head:', htmlStructure.hasHead);
    console.log('Has body:', htmlStructure.hasBody);
    console.log('Has root element:', htmlStructure.hasRoot);
    console.log('Number of scripts:', htmlStructure.scripts.length);
    console.log('Scripts with src:', htmlStructure.scripts.filter(s => s.src).length);
    console.log('Inline scripts:', htmlStructure.scripts.filter(s => s.hasContent).length);
    
    if (htmlStructure.rootContent) {
      console.log('Root element content length:', htmlStructure.rootContent.length);
      console.log('Root content preview:', htmlStructure.rootContent.substring(0, 200));
    }

    expect(true).toBe(true);
  });
});
