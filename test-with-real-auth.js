#!/usr/bin/env node

/**
 * Test Bento Grid with Real Authentication
 * 
 * Uses the actual test user accounts to authenticate and test the bento grid
 */

const { chromium } = require('playwright');

// Test user credentials
const TEST_USERS = {
  regular: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  admin: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  // Backup accounts
  backup: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
};

async function testBentoGridWithRealAuth() {
  console.log('🔐 Testing Bento Grid with Real Authentication');
  console.log('='.repeat(50));

  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('🌐 Loading application...');
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');

    // Take screenshot of initial state
    await page.screenshot({ path: 'test-results/real-auth-initial.png', fullPage: true });

    // Check if login is needed
    const emailInput = page.locator('input[type="email"]').first();
    const needsAuth = await emailInput.isVisible({ timeout: 5000 });

    if (needsAuth) {
      console.log('📝 Login form detected, authenticating...');
      
      // Use regular test user
      const user = TEST_USERS.regular;
      console.log(`🔑 Logging in as: ${user.email}`);
      
      // Fill login form
      await emailInput.fill(user.email);
      await page.fill('input[type="password"]', user.password);
      
      // Take screenshot before submitting
      await page.screenshot({ path: 'test-results/real-auth-form-filled.png', fullPage: true });
      
      // Submit login
      const submitButton = page.locator('button[type="submit"]').first();
      await submitButton.click();
      
      console.log('⏳ Waiting for authentication...');
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(5000); // Give extra time for auth
      
      // Take screenshot after auth attempt
      await page.screenshot({ path: 'test-results/real-auth-after-submit.png', fullPage: true });
      
      // Check if we're still on login page
      const stillOnLogin = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
      
      if (stillOnLogin) {
        console.log('❌ Still on login page, checking for errors...');

        // Look for error messages
        const errorMessages = await page.locator('[class*="error"], [class*="alert"], .text-red-500').allTextContents();
        if (errorMessages.length > 0) {
          console.log('🚨 Error messages found:', errorMessages);
        }

        // Try to find a "Sign Up" or "Create Account" link
        const signUpLink = page.locator('a, button').filter({ hasText: /sign up|create|register/i });
        const hasSignUp = await signUpLink.count() > 0;

        if (hasSignUp) {
          console.log('🔗 Found sign up option, attempting to create account...');

          try {
            await signUpLink.first().click();
            await page.waitForLoadState('networkidle');

            // Fill sign up form if it appears
            const signUpEmail = page.locator('input[type="email"]').first();
            const signUpPassword = page.locator('input[type="password"]').first();

            if (await signUpEmail.isVisible() && await signUpPassword.isVisible()) {
              console.log('📝 Filling sign up form...');
              await signUpEmail.fill(user.email);
              await signUpPassword.fill(user.password);

              // Look for confirm password field
              const confirmPassword = page.locator('input[type="password"]').nth(1);
              if (await confirmPassword.isVisible()) {
                await confirmPassword.fill(user.password);
              }

              // Submit sign up
              const signUpSubmit = page.locator('button[type="submit"]').first();
              await signUpSubmit.click();
              await page.waitForLoadState('networkidle');
              await page.waitForTimeout(3000);

              console.log('✅ Account creation attempted');
            }
          } catch (signUpError) {
            console.log('⚠️  Sign up attempt failed:', signUpError.message);
          }
        }

        // Try backup account if main account fails
        console.log('🔄 Trying backup account...');
        const backupUser = TEST_USERS.backup;

        try {
          await page.goto('http://localhost:5173');
          await page.waitForLoadState('networkidle');

          const backupEmailInput = page.locator('input[type="email"]').first();
          if (await backupEmailInput.isVisible()) {
            await backupEmailInput.fill(backupUser.email);
            await page.fill('input[type="password"]', backupUser.password);
            await page.click('button[type="submit"]');
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(3000);

            const stillOnLoginBackup = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
            if (!stillOnLoginBackup) {
              console.log('✅ Backup account authentication successful!');
            } else {
              console.log('⚠️  Backup account also failed, continuing test...');
            }
          }
        } catch (backupError) {
          console.log('⚠️  Backup authentication failed:', backupError.message);
        }

      } else {
        console.log('✅ Authentication appears successful!');
      }
      
    } else {
      console.log('✅ No login required or already authenticated');
    }

    // Now look for bento grid elements
    console.log('\n🔍 Looking for bento grid elements...');
    
    const bentoSelectors = [
      '[data-canvas-card]',
      '.grid',
      '[data-testid="experimental-navigation"]',
      '.card',
      '[class*="card"]',
      '[role="gridcell"]'
    ];

    let foundBentoGrid = false;
    let workingSelector = '';
    
    for (const selector of bentoSelectors) {
      const count = await page.locator(selector).count();
      if (count > 0) {
        console.log(`✅ Found ${count} elements with selector: ${selector}`);
        foundBentoGrid = true;
        workingSelector = selector;
        break;
      } else {
        console.log(`❌ No elements found: ${selector}`);
      }
    }

    if (foundBentoGrid) {
      console.log(`\n🎯 Bento grid found! Using selector: ${workingSelector}`);
      
      // Take screenshot of the bento grid
      await page.screenshot({ path: 'test-results/real-auth-bento-grid.png', fullPage: true });
      
      // Get all the tiles
      const tiles = page.locator(workingSelector);
      const tileCount = await tiles.count();
      console.log(`📊 Found ${tileCount} bento grid tiles`);
      
      // Test clicking the first few tiles
      console.log('\n🖱️  Testing tile navigation...');
      
      for (let i = 0; i < Math.min(tileCount, 5); i++) {
        const tile = tiles.nth(i);
        
        try {
          // Get tile text for identification
          const tileText = await tile.textContent();
          console.log(`\n  Testing tile ${i + 1}: "${tileText?.substring(0, 30)}..."`);
          
          // Click the tile
          await tile.click();
          await page.waitForLoadState('networkidle');
          
          // Check the URL
          const currentUrl = page.url();
          console.log(`    ✅ Navigated to: ${currentUrl}`);
          
          // Take a screenshot
          await page.screenshot({ 
            path: `test-results/real-auth-tile-${i + 1}-result.png`, 
            fullPage: true 
          });
          
          // Go back to home
          await page.goto('http://localhost:5173');
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
        } catch (error) {
          console.log(`    ❌ Error testing tile ${i + 1}: ${error.message}`);
        }
      }
      
      console.log('\n🎉 Bento grid navigation testing completed!');
      
    } else {
      console.log('\n❌ No bento grid elements found');
      console.log('🔍 Checking what IS on the page...');
      
      // Get page content
      const bodyText = await page.locator('body').textContent();
      console.log(`Page content preview: ${bodyText?.substring(0, 300)}...`);
      
      // Check for any navigation elements
      const buttons = await page.locator('button').count();
      const links = await page.locator('a').count();
      const divs = await page.locator('div').count();
      
      console.log(`Found: ${buttons} buttons, ${links} links, ${divs} divs`);
      
      // Look for specific content
      const hasWelcome = bodyText?.includes('Welcome');
      const hasDashboard = bodyText?.includes('Dashboard');
      const hasNavigation = bodyText?.includes('Navigation');
      const hasLogin = bodyText?.includes('Login') || bodyText?.includes('Sign in');
      
      console.log(`Content indicators:`);
      console.log(`  Welcome: ${hasWelcome}`);
      console.log(`  Dashboard: ${hasDashboard}`);
      console.log(`  Navigation: ${hasNavigation}`);
      console.log(`  Login: ${hasLogin}`);
    }

    // Keep browser open for manual inspection
    console.log('\n👀 Browser will stay open for 30 seconds for manual inspection...');
    console.log('You can manually interact with the page to test more features!');
    await page.waitForTimeout(30000);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    await page.screenshot({ path: 'test-results/real-auth-error.png', fullPage: true });
  } finally {
    await browser.close();
    console.log('\n✅ Test completed!');
    console.log('\n📋 Summary:');
    console.log('- Used real test account authentication');
    console.log('- Tested bento grid navigation');
    console.log('- Screenshots saved in test-results/');
    console.log('\n📸 Check these screenshots:');
    console.log('  - real-auth-initial.png (initial page)');
    console.log('  - real-auth-form-filled.png (login form)');
    console.log('  - real-auth-after-submit.png (after login)');
    console.log('  - real-auth-bento-grid.png (bento grid if found)');
    console.log('  - real-auth-tile-*-result.png (navigation results)');
  }
}

// Run the test
testBentoGridWithRealAuth().catch(console.error);
