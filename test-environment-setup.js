// Test script to verify environment setup and API connections
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { readFileSync, existsSync } from 'fs';

// Load environment variables from client/.env.local
dotenv.config({ path: 'client/.env.local' });

console.log('🧪 Testing Environment Setup & API Connections\n');

// Test 1: Environment Variables
console.log('1️⃣ Testing Environment Variables...');
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_GA_TRACKING_ID',
  'TELLER_APPLICATION_ID',
  'GOOGLE_CLIENT_ID',
  'GITHUB_CLIENT_ID'
];

let envTestsPassed = 0;
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value && value !== 'your_' + varName.toLowerCase() + '_here') {
    console.log(`   ✅ ${varName}: Configured`);
    envTestsPassed++;
  } else {
    console.log(`   ❌ ${varName}: Missing or placeholder`);
  }
});

console.log(`   📊 Environment Variables: ${envTestsPassed}/${requiredEnvVars.length} configured\n`);

// Test 2: Supabase Connection
console.log('2️⃣ Testing Supabase Connection...');
try {
  const supabase = createClient(
    process.env.VITE_SUPABASE_URL,
    process.env.VITE_SUPABASE_ANON_KEY
  );

  // Test basic connection
  const { data, error } = await supabase
    .from('users')
    .select('count')
    .limit(1);

  if (error) {
    console.log(`   ❌ Supabase connection failed: ${error.message}`);
  } else {
    console.log('   ✅ Supabase connection successful');
    
    // Test authentication
    const { data: authData, error: authError } = await supabase.auth.getSession();
    if (authError) {
      console.log(`   ⚠️ Auth check failed: ${authError.message}`);
    } else {
      console.log('   ✅ Supabase auth system accessible');
    }
  }
} catch (err) {
  console.log(`   ❌ Supabase test exception: ${err.message}`);
}

// Test 3: Teller Certificate Files
console.log('\n3️⃣ Testing Teller Certificate Files...');
const tellerCertPath = process.env.TELLER_CERTIFICATE_PATH || './teller/certificate.pem';
const tellerKeyPath = process.env.TELLER_PRIVATE_KEY_PATH || './teller/private_key.pem';

if (existsSync(tellerCertPath)) {
  console.log('   ✅ Teller certificate file found');
  try {
    const certContent = readFileSync(tellerCertPath, 'utf8');
    if (certContent.includes('BEGIN CERTIFICATE')) {
      console.log('   ✅ Teller certificate format valid');
    } else {
      console.log('   ❌ Teller certificate format invalid');
    }
  } catch (err) {
    console.log(`   ❌ Error reading certificate: ${err.message}`);
  }
} else {
  console.log(`   ❌ Teller certificate not found at: ${tellerCertPath}`);
}

if (existsSync(tellerKeyPath)) {
  console.log('   ✅ Teller private key file found');
  try {
    const keyContent = readFileSync(tellerKeyPath, 'utf8');
    if (keyContent.includes('BEGIN PRIVATE KEY') || keyContent.includes('BEGIN RSA PRIVATE KEY')) {
      console.log('   ✅ Teller private key format valid');
    } else {
      console.log('   ❌ Teller private key format invalid');
    }
  } catch (err) {
    console.log(`   ❌ Error reading private key: ${err.message}`);
  }
} else {
  console.log(`   ❌ Teller private key not found at: ${tellerKeyPath}`);
}

// Test 4: Google Analytics Configuration
console.log('\n4️⃣ Testing Google Analytics Configuration...');
const gaTrackingId = process.env.VITE_GA_TRACKING_ID;
if (gaTrackingId && gaTrackingId.startsWith('G-')) {
  console.log('   ✅ Google Analytics tracking ID format valid');
} else {
  console.log('   ❌ Google Analytics tracking ID invalid or missing');
}

// Test 5: OAuth Configuration
console.log('\n5️⃣ Testing OAuth Configuration...');
const googleClientId = process.env.GOOGLE_CLIENT_ID;
const githubClientId = process.env.GITHUB_CLIENT_ID;

if (googleClientId && googleClientId.includes('.apps.googleusercontent.com')) {
  console.log('   ✅ Google OAuth Client ID format valid');
} else {
  console.log('   ❌ Google OAuth Client ID invalid or missing');
}

if (githubClientId && githubClientId.startsWith('Ov23li')) {
  console.log('   ✅ GitHub OAuth Client ID format valid');
} else {
  console.log('   ❌ GitHub OAuth Client ID invalid or missing');
}

// Test 6: Project Management APIs
console.log('\n6️⃣ Testing Project Management APIs...');
const jiraToken = process.env.JIRA_API_TOKEN;
const trelloKey = process.env.TRELLO_API_KEY;

if (jiraToken && jiraToken.startsWith('ATATT')) {
  console.log('   ✅ Jira API token format valid');
} else {
  console.log('   ❌ Jira API token invalid or missing');
}

if (trelloKey && trelloKey.length === 32) {
  console.log('   ✅ Trello API key format valid');
} else {
  console.log('   ❌ Trello API key invalid or missing');
}

// Summary
console.log('\n' + '='.repeat(50));
console.log('📋 ENVIRONMENT SETUP SUMMARY');
console.log('='.repeat(50));

const totalTests = 6;
let passedTests = 0;

if (envTestsPassed >= 4) passedTests++;
if (process.env.VITE_SUPABASE_URL) passedTests++;
if (existsSync(tellerCertPath)) passedTests++;
if (gaTrackingId) passedTests++;
if (googleClientId && githubClientId) passedTests++;
if (jiraToken && trelloKey) passedTests++;

console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
console.log(`📊 Setup Completion: ${Math.round((passedTests/totalTests) * 100)}%`);

if (passedTests >= 4) {
  console.log('\n🎉 Environment setup is ready for development!');
  console.log('🚀 You can now start the development server with: npm run dev');
} else {
  console.log('\n⚠️ Some configuration issues found. Please check the errors above.');
  console.log('📚 Refer to API_KEYS_MASTER.md for missing configuration.');
}

console.log('\n🔗 Next Steps:');
console.log('1. Fix any missing environment variables');
console.log('2. Test the application: cd client && npm run dev');
console.log('3. Verify all pages load without errors');
console.log('4. Test authentication flows');
console.log('5. Test payment integration (Teller)');

console.log('\n' + '='.repeat(50));
